# huggingface_ibp/bound_core.py

import torch

class BoundTensor:
    """
    Holds lower and upper bounds for a tensor.
    """
    def __init__(self, lower: torch.Tensor, upper: torch.Tensor):
        self.lower = lower
        self.upper = upper

    def shape(self):
        return self.lower.shape

    def clone(self):
        return BoundTensor(self.lower.clone(), self.upper.clone())

def clamp_min(t: torch.Tensor, min_val: float):
    return torch.clamp(t, min=min_val)

def clamp_max(t: torch.Tensor, max_val: float):
    return torch.clamp(t, max=max_val)