# huggingface_ibp/bound_layers.py

import torch
import torch.nn as nn

from .bound_core import BoundTensor

class BoundLinear(nn.Module):
    def __init__(self, linear_layer: nn.Linear):
        """
        Wraps a PyTorch nn.Linear layer for IBP.
        """
        super().__init__()
        # Copy over the weight and bias from the original layer
        self.weight = nn.Parameter(linear_layer.weight.data.clone())
        if linear_layer.bias is not None:
            self.bias = nn.Parameter(linear_layer.bias.data.clone())
        else:
            self.bias = None

    def forward(self, x: BoundTensor) -> BoundTensor:
        """
        x: BoundTensor with x.lower, x.upper
        returns: BoundTensor with y.lower, y.upper
        """
        W = self.weight
        b = self.bias
        x_lower, x_upper = x.lower, x.upper

        W_pos = torch.clamp(W, min=0)
        W_neg = torch.clamp(W, max=0)

        # y_lower = W^+ * x_lower + W^- * x_upper + b
        y_lower = torch.matmul(x_lower, W_pos.transpose(0, 1)) + torch.matmul(x_upper, W_neg.transpose(0, 1))
        if b is not None:
            y_lower += b

        # y_upper = W^+ * x_upper + W^- * x_lower + b
        y_upper = torch.matmul(x_upper, W_pos.transpose(0, 1)) + torch.matmul(x_lower, W_neg.transpose(0, 1))
        if b is not None:
            y_upper += b

        return BoundTensor(y_lower, y_upper)
    
class BoundReLU(nn.Module):
    def forward(self, x: BoundTensor) -> BoundTensor:
        return BoundTensor(
            torch.relu(x.lower),
            torch.relu(x.upper)
        )