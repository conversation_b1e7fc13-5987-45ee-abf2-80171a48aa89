# huggingface_ibp/bound_bert.py

import torch
import torch.nn as nn
from transformers import BertModel

from .bound_core import BoundTensor
from .bound_layers import BoundLinear, BoundReLU

class BoundBertFeedForward(nn.Module):
    """
    A bounding-aware version of the feed-forward sub-block inside a transformer layer:
        Transformer FFN:  (Linear -> GeLU -> Linear)
    We'll approximate it with ReLU for simplicity here.
    """
    def __init__(self, intermediate_layer: nn.Linear, output_layer: nn.Linear):
        super().__init__()
        # Convert the standard linear layers into bound-aware linear layers
        self.intermediate_linear = BoundLinear(intermediate_layer)
        self.activation = BoundReLU()  # stand-in for ReLU or GeLU bounding
        self.output_linear = BoundLinear(output_layer)

    def forward(self, x: BoundTensor) -> BoundTensor:
        # 1. Bound pass through the intermediate linear
        x = self.intermediate_linear(x)
        # 2. Bound pass through the activation
        x = self.activation(x)
        # 3. Bound pass through the output linear
        x = self.output_linear(x)
        return x

class BoundBertLayer(nn.Module):
    """
    A bounding-aware version of a single Bert<PERSON>ayer (minus the attention bounding).
    """
    def __init__(self, bert_layer: nn.Module):
        super().__init__()
        # Typically, bert_layer has:
        #   - bert_layer.attention
        #   - bert_layer.intermediate
        #   - bert_layer.output
        #
        # We'll skip attention for now, so let's store it but not apply bounding to it.
        self.attention = bert_layer.attention  # not bounding yet!

        # Now create our bounding feed-forward
        self.feed_forward = BoundBertFeedForward(
            bert_layer.intermediate.dense,  # intermediate layer
            bert_layer.output.dense         # output layer
        )

        # We can store the layer norm as is, or write a bounding version later
        self.post_attention_layer_norm = bert_layer.output.LayerNorm

    def forward(self, x_bounds: BoundTensor) -> BoundTensor:
        """
        x_bounds: bounding on the input to this layer.
        For real BERT, the attention block is x -> attention -> add & norm -> feed-forward -> add & norm.
        We'll skip bounding attention for now and do a pass-thru.

        We'll do something super naive:
          1) We run the standard attention in "normal" mode on the center (or lower) of the bounds
          2) We treat that output as a constant for now
          3) Then we pass the result to the bounding feed-forward
        This is not correct bounding for the attention portion, but it illustrates the pipeline.
        """
        x_center = (x_bounds.lower + x_bounds.upper) / 2.0
        # Reshape / handle attention inputs
        attn_output = self.attention(x_center)[0]  # shape [batch_size, seq_len, hidden_dim]

        # post-attn residual + layer norm
        # we treat attn_output as if it’s the new "x" for bounding
        # REALLY, you'd need bounding logic for attention + LN here
        # We'll do naive pass: treat attn_output as a constant => lower=upper=attn_output
        attn_bounds = BoundTensor(attn_output, attn_output)
        # If there's a layer norm, we also skip bounding it for brevity
        attn_bounds_lower = self.post_attention_layer_norm(attn_bounds.lower)
        attn_bounds_upper = self.post_attention_layer_norm(attn_bounds.upper)
        attn_bounds = BoundTensor(attn_bounds_lower, attn_bounds_upper)

        # Next: feed forward bounding
        ff_out = self.feed_forward(attn_bounds)
        # BERT adds a residual and another layer norm, etc. 
        # We'll skip that detail for the example
        return ff_out


class BoundBertModel(nn.Module):
    """
    A bounding-aware wrapper around the main BertModel (skipping attention bounding).
    """
    def __init__(self, hf_bert_model: BertModel):
        super().__init__()
        self.embeddings = hf_bert_model.embeddings  # We'll treat embeddings as standard
        # Build bounding layers for each Transformer block
        self.bound_layers = nn.ModuleList(
            [BoundBertLayer(layer_module) for layer_module in hf_bert_model.encoder.layer]
        )

    def forward(self, input_bounds: BoundTensor) -> BoundTensor:
        """
        input_bounds: bounding on the *embedding space* of shape [batch, seq_len, hidden_dim].
        We'll pass it through each BoundBertLayer in sequence (skipping real attention bounding).
        """
        # For demonstration, let's skip bounding the embedding step and assume input_bounds
        # is already the post-embedding representation.

        x_bounds = input_bounds
        for layer in self.bound_layers:
            x_bounds = layer(x_bounds)

        return x_bounds

    @classmethod
    def from_pretrained(cls, model_name_or_path: str):
        hf_bert = BertModel.from_pretrained(model_name_or_path)
        return cls(hf_bert)