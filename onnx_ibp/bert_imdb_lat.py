import torch
import torch.nn as nn
import torch.optim as optim
from transformers import <PERSON><PERSON>oken<PERSON>, BertForSequenceClassification
from torch.utils.data import DataLoader
from datasets import load_dataset
from sklearn.metrics import accuracy_score
from tqdm import tqdm
from main_bertc import *

def find_adversarial_example(model, tokenizer, criterion, text, label, epsilon, alpha, num_steps):
    inputs = tokenizer(text, padding="max_length", truncation=True, max_length=250, return_tensors="pt").to(device)

    # Forward pass to get H_10
    with torch.no_grad():
        outputs = model.bert(**inputs, output_hidden_states=True)
        hidden_states = outputs.hidden_states
        H_10 = hidden_states[10]  # Shape: [1, seq_len, hidden_dim]

    H_adv = H_10.clone().detach().requires_grad_(True)

    # Freeze model parameters
    for param in model.parameters():
        param.requires_grad = False

    # PGD Loop
    for _ in range(num_steps):
        # Forward pass through layers 11 and 12
        attention_mask = inputs['attention_mask']
        extended_mask = model.bert.get_extended_attention_mask(attention_mask, H_adv.shape[:-1], device=H_adv.device)
        H_11 = model.bert.encoder.layer[10](H_adv, extended_mask)[0]
        H_12 = model.bert.encoder.layer[11](H_11, extended_mask)[0]

        # Final classifier head
        cls_output = model.bert.pooler(H_12)
        logits = model.classifier(cls_output)

        # Compute loss and gradient
        loss = criterion(logits, label)
        loss.backward()

        # PGD update
        grad = H_adv.grad.data
        H_adv = H_adv + alpha * torch.sign(grad)
        H_adv = torch.max(torch.min(H_adv, H_10 + epsilon), H_10 - epsilon)  # Project back to epsilon ball
        H_adv = H_adv.detach().requires_grad_()  # Detach and enable grad again
    
    return H_adv, extended_mask

# Training function
def train(model, dataloader, optimizer, criterion, device):
    model.train()
    total_loss = 0
    progress_bar = tqdm(dataloader, desc="Training")
    i = 0
    for batch in progress_bar:
        i += 1
        optimizer.zero_grad()
        text = batch["text"]
        labels = batch["label"].to(device)

        H_adv, extended_mask = find_adversarial_example(model, tokenizer, criterion, text, labels, epsilon, alpha, num_steps)

        # unfreeze model parameters
        for param in model.parameters():
            param.requires_grad = True

        # Use H_adv for downstream prediction
        H_11 = model.bert.encoder.layer[10](H_adv, extended_mask)[0]
        H_12 = model.bert.encoder.layer[11](H_11, extended_mask)[0]
        cls_output = model.bert.pooler(H_12)
        logits = model.classifier(cls_output)
        
        loss = criterion(logits, labels)
        loss.backward()
        optimizer.step()

        total_loss += loss.item()
        progress_bar.set_postfix(loss=total_loss / i) 
    return total_loss / len(dataloader)

# Evaluation function
def evaluate(model, dataloader, device):
    model.eval()
    all_preds, all_labels = [], []
    with torch.no_grad():
        for batch in tqdm(dataloader, desc="Evaluating"):
            text = batch["text"]
            inputs = tokenizer(text, padding="max_length", truncation=True, max_length=250, return_tensors="pt").to(device)
            labels = batch["label"].to(device)
            

            outputs = model(**inputs)
            preds = torch.argmax(outputs.logits, dim=1)

            all_preds.extend(preds.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())

    return accuracy_score(all_labels, all_preds)


# PGD config
epsilon = 1e-1
alpha = 1e-2
num_steps = 10


import os
os.environ['HF_HOME'] = '/projects/bdss/enyij2/cache/huggingface/'
# Set device (Change to "cuda:1" if using multiple GPUs)
device = torch.device("cuda:2" if torch.cuda.is_available() else "cpu")

# Load IMDB dataset
dataset = load_dataset("imdb")

# Load BERT tokenizer
tokenizer = BertTokenizer.from_pretrained("fabriceyhc/bert-base-uncased-imdb")

# Create DataLoader
BATCH_SIZE = 1
# .select(range(2000))
train_loader = DataLoader(dataset["train"].shuffle(seed=42), batch_size=BATCH_SIZE, shuffle=True)
test_loader = DataLoader(dataset["test"].shuffle(seed=42), batch_size=BATCH_SIZE, shuffle=False)

# Load pre-trained BERT model with classification head
model = BertForSequenceClassification.from_pretrained("fabriceyhc/bert-base-uncased-imdb", num_labels=2)
model.to(device)

# Define optimizer and loss function
optimizer = optim.SGD(model.parameters(), lr=2e-5)
criterion = nn.CrossEntropyLoss()


# Training loop
EPOCHS = 1
for epoch in range(EPOCHS):
    train_loss = train(model, train_loader, optimizer, criterion, device)
    test_acc = evaluate(model, test_loader, device)
    print(f"Epoch {epoch+1}/{EPOCHS}, Train Loss: {train_loss:.4f}, Test Accuracy: {test_acc:.4f}")

# Save the fine-tuned model
model.save_pretrained("bert-imdb-model-lat")
tokenizer.save_pretrained("bert-imdb-model-lat")

print("Training complete. Model saved as 'bert-imdb-model-lat'.")
