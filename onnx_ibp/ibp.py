from onnx import helper
from transformers import AutoModel
from BoundClasses import *
import logging
import time 

# TODO: change this to BertForSequenceClassification
def get_bert_constants(model_dir):
    model = AutoModel.from_pretrained(model_dir)
    constants = {
        "bert.embeddings.position_ids": model.embeddings.position_ids,
        "bert.embeddings.word_embeddings.weight": model.embeddings.word_embeddings.weight,
        "bert.embeddings.token_type_embeddings.weight": model.embeddings.token_type_embeddings.weight,
        "bert.embeddings.position_embeddings.weight": model.embeddings.position_embeddings.weight,
        "bert.embeddings.LayerNorm.weight": model.embeddings.LayerNorm.weight,
        "bert.embeddings.LayerNorm.bias": model.embeddings.LayerNorm.bias,
    }
    del model
    return constants

def convert_bert_layers_to_bound(model, device):
    graph = model.graph
    bound_nodes = []
    input_names = [inp.name for inp in graph.input]
    output_names = [out.name for out in graph.output]

    for node in graph.node:
        op_type = node.op_type
        assert op_type in bound_op_map, f"Operation {op_type} not supported."
        attr_dict = {attr.name: helper.get_attribute_value(attr) for attr in node.attribute}
        attr_dict["device"] = device
        BoundClass = bound_op_map[op_type]
        bound_node = BoundClass(
            name=node.name,
            inputs=list(node.input),
            outputs=list(node.output),
            attr=attr_dict
        )
        bound_nodes.append(bound_node)

    return bound_nodes, input_names, output_names

def check_types(a, name):
    if a.dtype == torch.float32:
        return a.to(torch.float16)
    assert a.dtype in (torch.float16, torch.int64, torch.bool), f"Input '{name}' lower bound is not float16 or int64 (got {a.dtype})"
    return a

def IBP(bound_nodes, init_intervals, eps, perturb_operation, perturb_layer_idx, device, sigma_dict=None):
    '''
    - All tensors in `init_intervals` must be on the same device.
    - All tensors in `init_intervals` must be of type `float32` or `int64`.

    - `sigma_dict`: Used to replace layer normalization bounding with a constant standard deviation (std). 
    Can be retrieved from get_IBP_variance. Defaults to ones if not provided.

    Perturbation Options:
    - `perturb_operation`: Specifies the type of perturbation to apply. Possible values are:
        - "embed"
        - "Q"
        - "K"
        - "V"
        - "resid"

    - `perturb_layer_idx`: Specifies the index of the layer to apply the perturbation. Conditions are:
        - If `perturb_operation = "embed"`, `perturb_layer_idx` must be `None`.
        - If `perturb_operation` is one of {"Q", "K", "V", "resid"}, 
        `perturb_layer_idx` should be an integer within the range of layer indices in the model.
        - Negative indices are allowed, where `-1` refers to the last layer index.
    '''
    # logging.info(f"to perturb: {get_bert_node_name(perturb_operation, perturb_layer_idx)}")
    computed_intervals = {}
    tot_time_ibp = 0.0
    start_time = time.time()
    for node in bound_nodes:
        # logging.info(f"Node {node.name}")
        if "LayerNorm" in node.name:
            if not any(node.name.endswith(op_name) for op_name in ["/LayerNorm/ReduceMean", "/LayerNorm/Sub", "/LayerNorm/Mul", "/LayerNorm/Add_1", "/LayerNorm/Div"]):
                continue
        node_input_intervals = []
        for input_name in node.inputs:
            if input_name in init_intervals:
                lower, upper = init_intervals[input_name]
            elif input_name in computed_intervals:
                lower, upper = computed_intervals[input_name]
            elif input_name.endswith("/LayerNorm/Sqrt_output_0"):
                if sigma_dict is not None:
                    lower = torch.ones_like(lower, device=device) * sigma_dict[input_name]
                    upper = torch.ones_like(lower, device=device) * sigma_dict[input_name]
                else:
                    lower = torch.ones_like(lower, device=device)
                    upper = torch.ones_like(lower, device=device)
            else:
                raise ValueError(f"No interval found for input '{input_name}' of node '{node.name}'")
            # lower = check_types(lower, input_name)
            # upper = check_types(upper, input_name)
            # logging.info(f"input: {input_name}, {lower.median().item()}, {upper.median().item()}")
            node_input_intervals.append((lower, upper))
        
        
        outputs_interval = node.interval_propagate(*node_input_intervals)

        # start_time_node = time.time()
        for out_name in node.outputs:
            if out_name == get_bert_node_name(perturb_operation, perturb_layer_idx):
                outputs_interval = (outputs_interval[0] - eps, outputs_interval[1] + eps)
            computed_intervals[out_name] = outputs_interval
            # logging.info(f"output: {out_name}")
            # logging.info(f"lb: {outputs_interval[0].median().item()}")
            # logging.info(f"ub: {outputs_interval[1].median().item()}")
        # elapsed_time = time.time() - start_time_node
        # print(f"Time taken for node {node.name}: {elapsed_time:.4f} seconds")
        # tot_time_ibp += elapsed_time
        
    # print("ibp time: ", time.time() - start_time)
    # print("sum times: ", tot_time_ibp)
    lb, ub = computed_intervals["logits"]
    return lb, ub

def IBP_LAT(H_adv, bound_nodes, init_intervals, eps, perturb_operation, perturb_layer_idx, device, sigma_dict=None):
    '''
    - All tensors in `init_intervals` must be on the same device.
    - All tensors in `init_intervals` must be of type `float32` or `int64`.

    - `sigma_dict`: Used to replace layer normalization bounding with a constant standard deviation (std). 
    Can be retrieved from get_IBP_variance. Defaults to ones if not provided.

    Perturbation Options:
    - `perturb_operation`: Specifies the type of perturbation to apply. Possible values are:
        - "embed"
        - "Q"
        - "K"
        - "V"
        - "resid"

    - `perturb_layer_idx`: Specifies the index of the layer to apply the perturbation. Conditions are:
        - If `perturb_operation = "embed"`, `perturb_layer_idx` must be `None`.
        - If `perturb_operation` is one of {"Q", "K", "V", "resid"}, 
        `perturb_layer_idx` should be an integer within the range of layer indices in the model.
        - Negative indices are allowed, where `-1` refers to the last layer index.
    '''
    # logging.info(f"to perturb: {get_bert_node_name(perturb_operation, perturb_layer_idx)}")
    computed_intervals = {}
    tot_time_ibp = 0.0
    start_time = time.time()
    for node in bound_nodes:
        # logging.info(f"Node {node.name}")
        if "LayerNorm" in node.name:
            if not any(node.name.endswith(op_name) for op_name in ["/LayerNorm/ReduceMean", "/LayerNorm/Sub", "/LayerNorm/Mul", "/LayerNorm/Add_1", "/LayerNorm/Div"]):
                continue
        node_input_intervals = []
        for input_name in node.inputs:
            if input_name in init_intervals:
                lower, upper = init_intervals[input_name]
            elif input_name in computed_intervals:
                lower, upper = computed_intervals[input_name]
            elif input_name.endswith("/LayerNorm/Sqrt_output_0"):
                if sigma_dict is not None:
                    lower = torch.ones_like(lower, device=device) * sigma_dict[input_name]
                    upper = torch.ones_like(lower, device=device) * sigma_dict[input_name]
                else:
                    lower = torch.ones_like(lower, device=device)
                    upper = torch.ones_like(lower, device=device)
            else:
                raise ValueError(f"No interval found for input '{input_name}' of node '{node.name}'")
            # lower = check_types(lower, input_name)
            # upper = check_types(upper, input_name)
            # logging.info(f"input: {input_name}, {lower.median().item()}, {upper.median().item()}")
            node_input_intervals.append((lower, upper))
        
        
        outputs_interval = node.interval_propagate(*node_input_intervals)

        # start_time_node = time.time()
        for out_name in node.outputs:
            if out_name == get_bert_node_name(perturb_operation, perturb_layer_idx):
                outputs_interval = (H_adv - eps, H_adv + eps)
            computed_intervals[out_name] = outputs_interval
            # logging.info(f"output: {out_name}")
            # logging.info(f"lb: {outputs_interval[0].median().item()}")
            # logging.info(f"ub: {outputs_interval[1].median().item()}")
        # elapsed_time = time.time() - start_time_node
        # print(f"Time taken for node {node.name}: {elapsed_time:.4f} seconds")
        # tot_time_ibp += elapsed_time
        
    # print("ibp time: ", time.time() - start_time)
    # print("sum times: ", tot_time_ibp)
    lb, ub = computed_intervals["logits"]
    return lb, ub


def get_bert_node_name(node_type, layer_idx):
    NUM_BERT_LAYERS = 12
    
    if layer_idx < 0:
        layer_idx += NUM_BERT_LAYERS
    if not (0 <= layer_idx < NUM_BERT_LAYERS):
        raise IndexError(
            f"Layer index {layer_idx} is out of range for total_layers={NUM_BERT_LAYERS}."
        )
    
    node_templates = {
        "embed":     "/bert/embeddings/word_embeddings/Gather_output_0",
        "Q":         "/bert/encoder/layer.{layer}/attention/self/Reshape_output_0",
        "K":         "/bert/encoder/layer.{layer}/attention/self/Reshape_1_output_0",
        "V":         "/bert/encoder/layer.{layer}/attention/self/Reshape_2_output_0",
        "resid":     "/bert/encoder/layer.{layer}/output/Add_output_0"
    }
    
    if node_type not in node_templates:
        valid_keys = ", ".join(node_templates.keys())
        raise ValueError(
            f"Invalid node_type '{node_type}'. Must be one of: {valid_keys}."
        )
    template = node_templates[node_type]
    if "{layer}" in template:
        return template.format(layer=layer_idx)
    return template

def get_ibp_sigma(bound_nodes, init_intervals):
    computed_intervals = {}
    curr_sigma_dict = {}
    for node in bound_nodes:
        node_input_intervals = []
        for input_name in node.inputs:
            if input_name in init_intervals:
                lower, upper = init_intervals[input_name]
            elif input_name in computed_intervals:
                lower, upper = computed_intervals[input_name]
            else:
                raise ValueError(f"No interval found for input '{input_name}' of node '{node.name}'")
            if input_name.endswith("/LayerNorm/Sqrt_output_0"):
                curr_sigma_dict[input_name] = lower
                node_input_intervals.append((lower, upper))
            else:
                node_input_intervals.append((lower, upper))
        
        outputs_interval = node.interval_propagate(*node_input_intervals)
        out_name = node.outputs[0]
        computed_intervals[out_name] = outputs_interval

    return curr_sigma_dict
