import torch
import torch.nn as nn
import torch.optim as optim
from transformers import <PERSON><PERSON>oken<PERSON>, BertForSequenceClassification
from torch.utils.data import DataLoader
from datasets import load_dataset
from sklearn.metrics import accuracy_score
from tqdm import tqdm
from main_bertc import *
from textattack.attack_recipes import PWWSRen2019, TextFoolerJin2019, Pruthi2019, A2TYoo2021, CLARE2020
from textattack.datasets import Dataset
from textattack.models.wrappers import HuggingFaceModelWrapper

def generate_textfooler_attack(texts, labels, model, tokenizer):
    """
    Generate perturbed text using TextFooler attack.
    
    Args:
        text (str): Input text to perturb
        model: HuggingFace model
        tokenizer: HuggingFace tokenizer
    
    Returns:
        str: Perturbed text (or original text if attack fails)
    """
    perturbed_texts = []

    # Wrap model for TextAttack
    model_wrapper = HuggingFaceModelWrapper(model, tokenizer)
    
    # Create TextFooler attack
    attack = PWWSRen2019.build(model_wrapper)
    
    for text, label in zip(texts, labels):
        label = label.item()
        # Run attack
        result = attack.attack(text, label)
        
        if result.perturbed_text is not None:
            perturbed_texts.append(result.perturbed_text())
        else:
            perturbed_texts.append(text)
    
    return perturbed_texts

def generate_pruthi_attack(texts, labels, model, tokenizer):
    """
    Generate perturbed text using TextFooler attack.
    
    Args:
        text (str): Input text to perturb
        model: HuggingFace model
        tokenizer: HuggingFace tokenizer
    
    Returns:
        str: Perturbed text (or original text if attack fails)
    """
    perturbed_texts = []

    # Wrap model for TextAttack
    model_wrapper = HuggingFaceModelWrapper(model, tokenizer)
    
    # Create TextFooler attack
    attack = Pruthi2019.build(model_wrapper)
    
    for text, label in zip(texts, labels):
        label = label.item()
        # Run attack
        result = attack.attack(text, label)
        
        if result.perturbed_text is not None:
            perturbed_texts.append(result.perturbed_text())
        else:
            perturbed_texts.append(text)
    
    return perturbed_texts



import os
os.environ['HF_HOME'] = '/projects/bdss/enyij2/cache/huggingface/'
# Set device (Change to "cuda:1" if using multiple GPUs)
device = torch.device("cuda:3" if torch.cuda.is_available() else "cpu")

# Load IMDB dataset
dataset = load_dataset("imdb")

# Load BERT tokenizer
tokenizer = BertTokenizer.from_pretrained("fabriceyhc/bert-base-uncased-imdb")

# Create DataLoader
BATCH_SIZE = 1
train_loader = DataLoader(dataset["train"].shuffle(seed=42).select(range(2000)), batch_size=BATCH_SIZE, shuffle=True)
test_loader = DataLoader(dataset["test"].shuffle(seed=42), batch_size=BATCH_SIZE, shuffle=False)

# Load pre-trained BERT model with classification head
model = BertForSequenceClassification.from_pretrained("fabriceyhc/bert-base-uncased-imdb", num_labels=2)
model.to(device)

# Define optimizer and loss function
optimizer = optim.SGD(model.parameters(), lr=2e-5)
criterion = nn.CrossEntropyLoss()


# Training function
def train(model, dataloader, optimizer, criterion, device):
    model.train()
    total_loss = 0
    progress_bar = tqdm(dataloader, desc="Training")
    i = 0
    for batch in progress_bar:
        i += 1
        optimizer.zero_grad()
        text = batch["text"]
        labels = batch["label"].to(device)
        perturbed = generate_textfooler_attack(text, labels, model, tokenizer)
        inputs = tokenizer(perturbed, padding="max_length", truncation=True, max_length=250, return_tensors="pt").to(device)  

        outputs = model(**inputs)
        loss = criterion(outputs.logits, labels)
        loss.backward()
        optimizer.step()

        total_loss += loss.item()
        progress_bar.set_postfix(loss=total_loss / i) 
    return total_loss / len(dataloader)

# Evaluation function
def evaluate(model, dataloader, device):
    model.eval()
    all_preds, all_labels = [], []
    with torch.no_grad():
        for batch in tqdm(dataloader, desc="Evaluating"):
            text = batch["text"]
            inputs = tokenizer(text, padding="max_length", truncation=True, max_length=250, return_tensors="pt").to(device)
            labels = batch["label"].to(device)
            

            outputs = model(**inputs)
            preds = torch.argmax(outputs.logits, dim=1)

            all_preds.extend(preds.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())

    return accuracy_score(all_labels, all_preds)

# Training loop
EPOCHS = 1
for epoch in range(EPOCHS):
    train_loss = train(model, train_loader, optimizer, criterion, device)
    test_acc = evaluate(model, test_loader, device)
    print(f"Epoch {epoch+1}/{EPOCHS}, Train Loss: {train_loss:.4f}, Test Accuracy: {test_acc:.4f}")

# Save the fine-tuned model
model.save_pretrained("bert-imdb-model-adv-pwws")
tokenizer.save_pretrained("bert-imdb-model-adv-pwws")

print("Training complete. Model saved as 'bert-imdb-model-adv-pwws'.")