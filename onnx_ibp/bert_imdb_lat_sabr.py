# Epoch 1/3, Train Loss: 0.7415, Test Accuracy: 0.9183
import torch
import torch.nn as nn
import torch.optim as optim
from transformers import BertTokenizer, BertForSequenceClassification
from transformers import AutoModel, AutoModelForCausalLM
from torch.utils.data import DataLoader
from datasets import load_dataset
from sklearn.metrics import accuracy_score
from tqdm import tqdm
# from main_bertc import *
import torch.nn.functional as F
from copy import deepcopy
import onnx
import onnxruntime as ort
from onnx import helper, numpy_helper
import torch
import numpy as np
from transformers import AutoTokenizer, AutoModel, BertForSequenceClassification
from BoundClasses import *
from ibp import *
import logging 
import os
import time
import argparse

os.environ['HF_HOME'] = '/projects/bdss/enyij2/cache/huggingface/'
# Set device (Change to "cuda:1" if using multiple GPUs)
device = torch.device("cuda:2" if torch.cuda.is_available() else "cpu")
logging.getLogger("transformers.modeling_utils").setLevel(logging.ERROR)

def set_new_weights(model, initializer_dict):
    state_dict = model.state_dict()
    for name in state_dict:
        # print(name)
        if name in initializer_dict:
            # print(name)
            state_dict[name] = deepcopy(initializer_dict[name])
    model.load_state_dict(state_dict)
    return model

def adversarial_logit(y_hat, y, num_labels=2):
    batch_size = y.size(0)
    classes = torch.arange(num_labels, device=y.device).unsqueeze(0).expand(batch_size, -1)
    mask = (classes == y.unsqueeze(-1)).to(dtype=y_hat[0].dtype)
    adversarial_logit = (1 - mask) * y_hat[1] + mask * y_hat[0]
    return adversarial_logit

def find_adversarial_example(model, tokenizer, criterion, text, label, epsilon, alpha, num_steps):
    inputs = tokenizer(text, padding="max_length", truncation=True, max_length=250, return_tensors="pt").to(device)

    # Forward pass to get H_10
    with torch.no_grad():
        outputs = model.bert(**inputs, output_hidden_states=True)
        hidden_states = outputs.hidden_states
        H_10 = hidden_states[10]  # Shape: [1, seq_len, hidden_dim]

    H_adv = H_10.clone().detach().requires_grad_(True)

    # Freeze model parameters
    # for param in model.parameters():
    #     param.requires_grad = False

    # PGD Loop
    for _ in range(num_steps):
        # Forward pass through layers 11 and 12
        attention_mask = inputs['attention_mask']
        extended_mask = model.bert.get_extended_attention_mask(attention_mask, H_adv.shape[:-1], device=H_adv.device)
        H_11 = model.bert.encoder.layer[10](H_adv, extended_mask)[0]
        H_12 = model.bert.encoder.layer[11](H_11, extended_mask)[0]

        # Final classifier head
        cls_output = model.bert.pooler(H_12)
        logits = model.classifier(cls_output)

        # Compute loss and gradient
        loss = criterion(logits, label)
        loss.backward()

        # PGD update
        grad = H_adv.grad.data
        H_adv = H_adv + alpha * torch.sign(grad)
        H_adv = torch.max(torch.min(H_adv, H_10 + epsilon), H_10 - epsilon)  # Project back to epsilon ball
        H_adv = H_adv.detach().requires_grad_()  # Detach and enable grad again

    
    return H_adv, extended_mask


# Training function
def train_IBP(initializer_dict, optimizer, model_dir, bound_nodes, dataloader, criterion, device, perturb_layer_idx=-2, perturb_operation="resid", cur_epoch=0, num_epochs=2):
    # model.train()
    total_loss = 0
    progress_bar = tqdm(dataloader, desc="Training")
    i = 0

    # TODO: scale eps

    bert_constants = get_bert_constants(model_dir=model_dir)
    pos_embed = bert_constants["bert.embeddings.position_ids"]

    activation_list = []
    def resid_activation_hook(module, input, output):
        activation_list.append(output.detach().cpu())
    hook_handle = model.bert.encoder.layer[-2].output.register_forward_hook(resid_activation_hook)
    for _ in range(5):
        batch = next(iter(dataloader))
        text = batch["text"]
        inputs = tokenizer(text, padding="max_length", truncation=True, max_length=250, return_tensors="pt").to(device)
        _ = model(**inputs)
    hook_handle.remove()
    mean_activation = torch.mean(torch.stack(activation_list), dim=0)
    epsilon = eps * mean_activation.median().abs() * 1.1

    print(epsilon)
    cont = cur_epoch * len(dataloader)
    num_exp = len(dataloader)

    k = 0 if cur_epoch==0 else 0.5
    running_eps = 0 if cur_epoch==0 else epsilon

    for batch in progress_bar:
        # start_batch_time = time.time()
        i += 1
        optimizer.zero_grad()
        text = batch["text"]
        inputs = tokenizer(text, padding="max_length", truncation=True, max_length=250, return_tensors="pt").to(device)
        labels = batch["label"].to(device)
        onnx_inputs_torch = {
            "input_ids": inputs["input_ids"],
            "attention_mask": inputs["attention_mask"],
            # "token_type_ids": inputs_np["token_type_ids"].astype(np.int64),
        }
        mark1 = time.time()
        # print("mark1", mark1 - start_batch_time)
        seq_len = inputs["input_ids"].shape[1]
        bert_constants["bert.embeddings.position_ids"] = pos_embed[:, :seq_len]
        intervals_by_name = {k: (v.to(device), v.to(device)) for k, v in {**onnx_inputs_torch, **initializer_dict, **bert_constants}.items()}
        mark2 = time.time()
        # print("mark2", mark2 - mark1)
        H_adv, _ = find_adversarial_example(model, tokenizer, criterion, text, labels, eps_lat, alpha, num_steps)

        lb, ub = IBP_LAT(H_adv, bound_nodes, intervals_by_name, eps=running_eps, perturb_operation=perturb_operation, device=device, perturb_layer_idx=perturb_layer_idx)
        mark3 = time.time()
        # print("mark3", mark3 - mark2)
        # exit()
        lb, ub = lb[0][0], ub[0][0]
        logit = adversarial_logit([lb, ub], labels)
        loss_robust = criterion(logit, labels)

        if cont <= num_exp:
            running_eps += eps/num_exp
            k += 0.5/num_exp
            # print(running_eps, k)

        # loss natural with eps = 0
        lb, ub = IBP_LAT(H_adv, bound_nodes, intervals_by_name, eps=0, perturb_operation=perturb_operation, device=device, perturb_layer_idx=perturb_layer_idx)
        lb, ub = lb[0][0], ub[0][0]
        logit = adversarial_logit([lb, ub], labels)
        loss_nat = criterion(logit, labels)
        loss = (1-k) * loss_nat + k * loss_robust
        loss.backward()
        optimizer.step()
        mark4 = time.time()
        # print("mark4", mark4 - mark3)

        total_loss += loss.item()

        progress_bar.set_postfix(loss=total_loss / i) 

        cont += 1

    return total_loss / len(dataloader), initializer_dict

# Evaluation function
def evaluate(model, dataloader, device):
    model.eval()
    all_preds, all_labels = [], []
    with torch.no_grad():
        for batch in tqdm(dataloader, desc="Evaluating"):
            text = batch["text"]
            inputs = tokenizer(text, padding="max_length", truncation=True, max_length=250, return_tensors="pt").to(device)
            labels = batch["label"].to(device)

            outputs = model(**inputs)
            preds = torch.argmax(outputs.logits, dim=1)

            all_preds.extend(preds.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
    return accuracy_score(all_labels, all_preds)

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument('--eps', type=float, default=0.000001, help='Your age')
    args = parser.parse_args()

    # Training loop
    EPOCHS = 2
    model_dir = "fabriceyhc/bert-base-uncased-imdb" # "textattack/bert-base-uncased-imdb"
    perturb_operation = "resid"
    perturb_layer_idx = -2
    NUM_BERT_LAYERS = 12

    # Load IMDB dataset
    dataset = load_dataset("imdb")

    # Load BERT tokenizer
    tokenizer = AutoTokenizer.from_pretrained("textattack/bert-base-uncased-imdb")

    # Create DataLoader
    BATCH_SIZE = 1
    # .shuffle(seed=42).select(range(50))
    # .shuffle(seed=42).select(range(500))
    train_loader = DataLoader(dataset["train"].shuffle(seed=42), batch_size=BATCH_SIZE, shuffle=True)
    test_loader = DataLoader(dataset["test"].shuffle(seed=42), batch_size=BATCH_SIZE, shuffle=False)

    # Load pre-trained BERT model with classification head
    model = BertForSequenceClassification.from_pretrained("textattack/bert-base-uncased-imdb", num_labels=2, from_flax=True)
    model = model.to(device)

    # eps = 0.0
    eps = args.eps
    criterion = nn.CrossEntropyLoss()
    # PGD config
    eps_lat = 1e-1
    alpha = 1e-2
    num_steps = 10

    # save bert model path
    text = "DPLM-2 is primarily designed for generating single-chain protein sequences and their corresponding 3D structures. While the current implementation focuses on single-chain proteins, the underlying architecture could potentially be extended to handle longer sequences or multi-chain complexes. However, such adaptations would require significant modifications to the model to accurately capture the intricate interactions and structural dependencies inherent in multi-chain assemblies. Future research is needed to explore these extensions and validate their effectiveness."
    # tokenizer = AutoTokenizer.from_pretrained(model_dir)
    inputs_np = tokenizer(
        text,
        return_tensors="np" 
    )
    input_ids = torch.from_numpy(inputs_np["input_ids"]).to(torch.long)
    attention_mask = torch.from_numpy(inputs_np["attention_mask"]).to(torch.long)
    dummy_input = (input_ids.to(device), attention_mask.to(device))
    # model = BertForSequenceClassification.from_pretrained(model_dir, num_labels=2)
    model.train()
    torch.onnx.export(
        model, 
        dummy_input,  # Input tensor
        "bert_classification_eps.onnx",  # Save file
        input_names=["input_ids", "attention_mask"],  # Input names
        output_names=["logits"],  # Output names
        dynamic_axes={
            "input_ids": {0: "batch_size", 1: "sequence_length"},
            "attention_mask": {0: "batch_size", 1: "sequence_length"},
            "logits": {0: "batch_size"}  # Output shape is dynamic based on batch size
        },
        training=torch.onnx.TrainingMode.TRAINING,
        do_constant_folding=False, 
        opset_version=14
    )
    print("BERT classification model saved as 'bert_classification_eps.onnx'")

    onnx_model_path = "bert_classification_eps.onnx"
    onnx_model = onnx.load(onnx_model_path)
    bound_nodes, input_names, output_names = convert_bert_layers_to_bound(onnx_model, device=device)
    # global initializer_dict
    initializer_dict = {}
    for tensor in onnx_model.graph.initializer:
        arr = numpy_helper.to_array(tensor)
        n = tensor.name
        require_g = None
        # print(n)
        if 'classifier' in n: # or
            require_g = True
            # print(n)
        elif 'embeddings' in n:
            require_g = False
            # print(n)
        elif 'pooler' in n:
            require_g = True
            # print(n)
        elif (int(n.split('.')[3]) >= NUM_BERT_LAYERS + perturb_layer_idx):
            require_g =True
            # print(n)
        else:
            require_g = False
            # print(n)
        # print(require_g)
        param_t = torch.tensor(arr, dtype=torch.float32, requires_grad=require_g, device=device)
        initializer_dict[tensor.name] = param_t
    # optimizer = optim.SGD(initializer_dict.values(), lr=2e-5)
    optimizer = optim.SGD(initializer_dict.values(), lr=2e-5)

    # print(evaluate(model, test_loader, device))
    for epoch in range(EPOCHS):
        train_loss, initializer_dict = train_IBP(initializer_dict, optimizer, model_dir, bound_nodes, train_loader, criterion, device, perturb_layer_idx=perturb_layer_idx, perturb_operation=perturb_operation, cur_epoch=epoch, num_epochs=EPOCHS)

        model = set_new_weights(model, initializer_dict)
        test_acc = evaluate(model, test_loader, device)

        print(f"Epoch {epoch+1}/{EPOCHS}, Train Loss: {train_loss:.4f}, Test Accuracy: {test_acc:.4f}")
    
    model.save_pretrained(f"bert-imdb-model-ibp-eps-{eps}-scaled-epoch-{epoch}-{perturb_layer_idx}-{EPOCHS}-lat-sabr")
    tokenizer.save_pretrained(f"bert-imdb-model-ibp-eps-{eps}-scaled-epoch-{epoch}-{perturb_layer_idx}-{EPOCHS}-lat-sabr")

    print(f"Training complete. Model saved as 'bert-imdb-model-ibp-eps-{eps}-lat-sabr'.")
