import torch
import torch.nn as nn
import torch.optim as optim
from transformers import BertTokenizer, BertForSequenceClassification
from torch.utils.data import DataLoader
from datasets import load_dataset
from sklearn.metrics import accuracy_score
from tqdm import tqdm
from main_bertc import *

import os
os.environ['HF_HOME'] = '/projects/bdss/enyij2/cache/huggingface/'
# Set device (Change to "cuda:1" if using multiple GPUs)
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# Load IMDB dataset
dataset = load_dataset("imdb")

# Load BERT tokenizer
tokenizer = BertTokenizer.from_pretrained("bert-base-uncased")

# Create DataLoader
BATCH_SIZE = 1
train_loader = DataLoader(dataset["train"], batch_size=BATCH_SIZE, shuffle=True)
test_loader = DataLoader(dataset["test"], batch_size=BATCH_SIZE, shuffle=False)

# Load pre-trained BERT model with classification head
model = BertForSequenceClassification.from_pretrained("bert-base-uncased", num_labels=2)
model.to(device)

# Define optimizer and loss function
optimizer = optim.SGD(model.parameters(), lr=2e-5)
criterion = nn.CrossEntropyLoss()


# Training function
def train(model, dataloader, optimizer, criterion, device):
    model.train()
    total_loss = 0
    progress_bar = tqdm(dataloader, desc="Training")
    i = 0
    for batch in progress_bar:
        i += 1
        optimizer.zero_grad()
        text = batch["text"]
        inputs = tokenizer(text, padding="max_length", truncation=True, max_length=250, return_tensors="pt").to(device)
        labels = batch["label"].to(device)

        outputs = model(**inputs)
        loss = criterion(outputs.logits, labels)
        loss.backward()
        optimizer.step()

        total_loss += loss.item()
        progress_bar.set_postfix(loss=total_loss / i) 
    return total_loss / len(dataloader)

# Evaluation function
def evaluate(model, dataloader, device):
    model.eval()
    all_preds, all_labels = [], []
    with torch.no_grad():
        for batch in tqdm(dataloader, desc="Evaluating"):
            text = batch["text"]
            inputs = tokenizer(text, padding="max_length", truncation=True, max_length=250, return_tensors="pt").to(device)
            labels = batch["label"].to(device)

            outputs = model(**inputs)
            preds = torch.argmax(outputs.logits, dim=1)

            all_preds.extend(preds.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())

    return accuracy_score(all_labels, all_preds)

# Training loop
EPOCHS = 3
for epoch in range(EPOCHS):
    train_loss = train(model, train_loader, optimizer, criterion, device)
    test_acc = evaluate(model, test_loader, device)
    print(f"Epoch {epoch+1}/{EPOCHS}, Train Loss: {train_loss:.4f}, Test Accuracy: {test_acc:.4f}")

# Save the fine-tuned model
model.save_pretrained("bert-imdb-model")
tokenizer.save_pretrained("bert-imdb-model")

print("Training complete. Model saved as 'bert-imdb-model'.")