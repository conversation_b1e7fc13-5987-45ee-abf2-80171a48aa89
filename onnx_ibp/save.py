import torch
import torch.nn as nn
import torch.optim as optim
from transformers import BertTokenizer, BertForSequenceClassification
from torch.utils.data import DataLoader
from datasets import load_dataset
from sklearn.metrics import accuracy_score
from tqdm import tqdm
from main_bertc import *

# Load BERT tokenizer
tokenizer = BertTokenizer.from_pretrained("fabriceyhc/bert-base-uncased-imdb")

# Load pre-trained BERT model with classification head
model = BertForSequenceClassification.from_pretrained("fabriceyhc/bert-base-uncased-imdb", num_labels=2)


# Save the fine-tuned model
model.save_pretrained("bert-imdb-model")
tokenizer.save_pretrained("bert-imdb-model")