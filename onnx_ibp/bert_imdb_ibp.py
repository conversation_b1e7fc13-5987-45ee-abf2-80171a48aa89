import torch
import torch.nn as nn
import torch.optim as optim
from transformers import AutoTokenizer, BertForSequenceClassification
from transformers import AutoModel, AutoModelForCausalLM
from torch.utils.data import DataLoader
from datasets import load_dataset
from sklearn.metrics import accuracy_score
from tqdm import tqdm
from ibp import *
import torch.nn.functional as F
from copy import deepcopy
import time

import os
os.environ['HF_HOME'] = '/projects/bdss/enyij2/cache/huggingface/'
# Set device (Change to "cuda:1" if using multiple GPUs)
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# Load IMDB dataset
dataset = load_dataset("imdb")

# Load BERT tokenizer
tokenizer = AutoTokenizer.from_pretrained("bert-base-uncased")

# Create DataLoader
BATCH_SIZE = 1
# .shuffle(seed=42).select(range(50))
# .shuffle(seed=42).select(range(500))
train_loader = DataLoader(dataset["train"].shuffle(seed=42), batch_size=BATCH_SIZE, shuffle=True)
test_loader = DataLoader(dataset["test"].shuffle(seed=42), batch_size=BATCH_SIZE, shuffle=False)

# Load pre-trained BERT model with classification head
model = BertForSequenceClassification.from_pretrained("bert-base-uncased", num_labels=2)
model.to(device)

# load onnx model
eps = 0


# Define optimizer and loss function
criterion = nn.CrossEntropyLoss()

# def one_hot_binary(label):
#     """Converts a single label (0 or 1) to a one-hot tensor of length 2."""
#     return F.one_hot(torch.tensor(label), num_classes=2)

def set_new_weights(model, weight_values):
    state_dict = model.state_dict()
    for name in state_dict:
        # print(name)
        if name in weight_values:
            # print(name)
            state_dict[name] = deepcopy(weight_values[name])
    
    model.load_state_dict(state_dict)

    return model


def adversarial_logit(y_hat, y, num_labels=2):
    batch_size = y.size(0)

    classes = torch.arange(num_labels, device=y.device).unsqueeze(0).expand(batch_size, -1)
    mask = (classes == y.unsqueeze(-1)).to(dtype=y_hat[0].dtype)

    # print(mask)

    # Take upper bound for logit of all but the correct class where you take the lower bound
    adversarial_logit = (1 - mask) * y_hat[1] + mask * y_hat[0]

    return adversarial_logit

# Training function
def train_IBP(initializer_dict, optimizer, model_dir, bound_nodes, dataloader, criterion, device):
    # model.train()
    total_loss = 0
    

    bert_constants = get_bert_constants(model_dir=model_dir)
    progress_bar = tqdm(dataloader, desc="Training")
    
    i = 0
    for batch in progress_bar:
        i += 1
        optimizer.zero_grad()

        text = batch["text"]
        # inputs = tokenizer(text, padding="max_length", truncation=True, max_length=250, return_tensors="np")
        inputs = tokenizer(text, padding="max_length", truncation=True, max_length=250, return_tensors="pt").to(device)
        labels = batch["label"].to(device)
        

        onnx_inputs_torch = {
        "input_ids": inputs["input_ids"],
        "attention_mask": inputs["attention_mask"],
        # "token_type_ids": inputs_np["token_type_ids"].astype(np.int64),
        }


        # Merge the dictionaries; for floating point tensors, we convert to half.
        combined_dict = {**onnx_inputs_torch, **initializer_dict, **bert_constants}
        intervals_by_name = {}

        for k, v in combined_dict.items():
            intervals_by_name[k] = (v.to(device), v.to(device))

        # st = time.time()
        lb, ub = IBP(bound_nodes, intervals_by_name, eps=eps, device='cuda')
        # print(time.time() - st)

        lb, ub = lb[0][0], ub[0][0]
        logit = adversarial_logit([lb, ub], labels)
        loss_robust = criterion(logit, labels)

        start_time = time.time()
        loss_robust.backward()
        ibp_time = time.time() - start_time
        print(f"Loss backward execution time: {ibp_time:.6f} seconds")
        # st = time.time()
        optimizer.step()
        # print(f"Optimizer step execution time: {(time.time() - st):.6f} seconds")
        
        

        total_loss += loss_robust.item()

        progress_bar.set_postfix(loss=total_loss / i) 
    return total_loss / len(dataloader), initializer_dict

# Evaluation function
def evaluate(model, dataloader, device):
    model.eval()
    all_preds, all_labels = [], []
    with torch.no_grad():
        for batch in tqdm(dataloader, desc="Evaluating"):
            text = batch["text"]
            inputs = tokenizer(text, padding="max_length", truncation=True, max_length=250, return_tensors="pt").to(device)
            labels = batch["label"].to(device)

            outputs = model(**inputs)
            preds = torch.argmax(outputs.logits, dim=1)

            all_preds.extend(preds.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())

    return accuracy_score(all_labels, all_preds)

# Training loop
EPOCHS = 3
model_dir = "bert-base-uncased"

# save bert model path
text = "DPLM-2 is primarily designed for generating single-chain protein sequences and their corresponding 3D structures. While the current implementation focuses on single-chain proteins, the underlying architecture could potentially be extended to handle longer sequences or multi-chain complexes. However, such adaptations would require significant modifications to the model to accurately capture the intricate interactions and structural dependencies inherent in multi-chain assemblies. Future research is needed to explore these extensions and validate their effectiveness."
# tokenizer = AutoTokenizer.from_pretrained(model_dir)
inputs_np = tokenizer(
    text,
    return_tensors="np" 
)
input_ids = torch.from_numpy(inputs_np["input_ids"]).to(torch.long)
attention_mask = torch.from_numpy(inputs_np["attention_mask"]).to(torch.long)
dummy_input = (input_ids.to(device), attention_mask.to(device))
# model = BertForSequenceClassification.from_pretrained(model_dir, num_labels=2)
model.train()
torch.onnx.export(
    model, 
    dummy_input,  # Input tensor
    "bert_classification_ibp.onnx",  # Save file
    input_names=["input_ids", "attention_mask"],  # Input names
    output_names=["logits"],  # Output names
    dynamic_axes={
        "input_ids": {0: "batch_size", 1: "sequence_length"},
        "attention_mask": {0: "batch_size", 1: "sequence_length"},
        "logits": {0: "batch_size"}  # Output shape is dynamic based on batch size
    },
    training=torch.onnx.TrainingMode.TRAINING,
    do_constant_folding=False, 
    opset_version=14
)
print("BERT classification model saved as 'bert_classification_ibp.onnx'")

onnx_model_path = "bert_classification_ibp.onnx"
onnx_model = onnx.load(onnx_model_path)
bound_nodes, input_names, output_names = convert_bert_layers_to_bound(onnx_model, device='cuda')
initializer_dict = {}
# matrices = []
for tensor in onnx_model.graph.initializer:
    arr = numpy_helper.to_array(tensor)
    param_t = torch.tensor(arr, dtype=torch.float32, requires_grad=True, device="cuda")
    # matrices.append(param_t)
    initializer_dict[tensor.name] = param_t

optimizer = optim.SGD(initializer_dict.values(), lr=2e-5)

for epoch in range(EPOCHS):
    train_loss, initializer_dict = train_IBP(initializer_dict, optimizer, model_dir, bound_nodes, train_loader, criterion, device)

    # load models' weight using weight_values
    model = set_new_weights(model, initializer_dict)

    test_acc = evaluate(model, test_loader, device)
    print(f"Epoch {epoch+1}/{EPOCHS}, Train Loss: {train_loss:.4f}, Test Accuracy: {test_acc:.4f}")

# Save the fine-tuned model
model.save_pretrained("bert-imdb-model-ibp")
tokenizer.save_pretrained("bert-imdb-model-ibp")

print("Training complete. Model saved as 'bert-imdb-model'.")