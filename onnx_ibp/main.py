# %%
import onnx
import onnxruntime as ort
from onnx import helper, numpy_helper
import torch
import numpy as np
from transformers import <PERSON>Tokenizer, AutoModel
from BoundClasses import *
import logging
import time

def get_bert_constants(model_dir, seq_len):
    model = AutoModel.from_pretrained(model_dir).half()
    constants = {
        "bert.embeddings.position_ids": model.embeddings.position_ids[:, :seq_len],
        "bert.embeddings.word_embeddings.weight": model.embeddings.word_embeddings.weight,
        "bert.embeddings.token_type_embeddings.weight": model.embeddings.token_type_embeddings.weight,
        "bert.embeddings.position_embeddings.weight": model.embeddings.position_embeddings.weight,
        "bert.embeddings.LayerNorm.weight": model.embeddings.LayerNorm.weight,
        "bert.embeddings.LayerNorm.bias": model.embeddings.LayerNorm.bias,
    }
    del model
    return constants

def convert_bert_layers_to_bound(model, device="cuda:0"):
    graph = model.graph
    bound_nodes = []
    input_names = [inp.name for inp in graph.input]
    output_names = [out.name for out in graph.output]

    for node in graph.node:
        op_type = node.op_type
        assert op_type in bound_op_map, f"Operation {op_type} not supported."
        attr_dict = {attr.name: helper.get_attribute_value(attr) for attr in node.attribute}
        attr_dict["device"] = device
        BoundClass = bound_op_map[op_type]
        bound_node = BoundClass(
            name=node.name,
            inputs=list(node.input),
            outputs=list(node.output),
            attr=attr_dict
        )
        bound_nodes.append(bound_node)

    return bound_nodes, input_names, output_names

def IBP(bound_nodes, intervals_by_name, device="cuda:0", eps=0):
    for key in intervals_by_name:
        lower, upper = intervals_by_name[key]
        intervals_by_name[key] = (lower.to(device), upper.to(device))

    for node in bound_nodes:
        assert isinstance(node, Bound)
        
        input_intervals = []
        for input_name in node.inputs:
            if input_name in intervals_by_name:
                lower, upper = intervals_by_name[input_name]
                input_intervals.append((lower, upper))
            else:
                raise ValueError(f"No interval found for input '{input_name}' of node '{node.name}'")
        
        logging.info(f"Node {node.name}")
        time_start = time.time()
        outputs_interval = node.interval_propagate(*input_intervals)
        logging.info(f"  Time: {time.time() - time_start:.6f} sec")

        if "/bert/embeddings/word_embeddings/Gather_output_0" in node.outputs:
            outputs_interval = (outputs_interval[0] - eps, outputs_interval[1] + eps)

        assert len(node.outputs) == 1
        intervals_by_name[node.outputs[0]] = (
            outputs_interval[0],
            outputs_interval[1]
        )

    lb, ub = intervals_by_name["logits"]
    return lb, ub

# %%

def main():
    # %%
    logging.basicConfig(level=logging.INFO, format='%(message)s')

    model_dir = "/home/<USER>/bert-base-uncased"
    onnx_model_path = f"{model_dir}/model.onnx"
    text = "Some random sample text here!"
    
    tokenizer = AutoTokenizer.from_pretrained(model_dir)
    model = onnx.load(onnx_model_path)
    session = ort.InferenceSession(onnx_model_path, providers=['CPUExecutionProvider'])

    inputs_np = tokenizer(text, return_tensors="np")
    onnx_inputs = {
        "input_ids": inputs_np["input_ids"].astype(np.int64),
        "attention_mask": inputs_np["attention_mask"].astype(np.int64),
        "token_type_ids": inputs_np["token_type_ids"].astype(np.int64),
    }
    start_time = time.time()
    outputs = session.run(None, onnx_inputs)
    onnx_time = time.time() - start_time
    print(f"ONNX forward pass time: {onnx_time:.6f} seconds")

    device = "cuda:0"

    onnx_inputs_torch = {k: torch.tensor(v) for k, v in onnx_inputs.items()}
    
    initializer_dict = {}
    for tensor in model.graph.initializer:
        arr = numpy_helper.to_array(tensor)
        param_t = torch.tensor(arr, dtype=torch.float32, requires_grad=True)
        initializer_dict[tensor.name] = param_t

    bert_constants = get_bert_constants(model_dir=model_dir, seq_len=inputs_np["input_ids"].shape[1])
    
    # Merge the dictionaries; for floating point tensors, we convert to half.
    combined_dict = {**onnx_inputs_torch, **initializer_dict, **bert_constants}
    intervals_by_name = {}
    for k, v in combined_dict.items():
        intervals_by_name[k] = (v.to(device), v.to(device))

    bound_nodes, input_names, output_names = convert_bert_layers_to_bound(model)
    
    # warmup
    lb, ub = IBP(bound_nodes, intervals_by_name, eps=0)
    start_time = time.time()
    lb, ub = IBP(bound_nodes, intervals_by_name, eps=0)
    ibp_time = time.time() - start_time
    print(f"IBP execution time: {ibp_time:.6f} seconds")
    
    print(torch.all(lb == ub))
    # print(torch.tensor(outputs[0]).cpu())
    # print(lb.cpu())
    print((torch.tensor(outputs[0]).cpu() - lb.cpu()).abs().max())


if __name__ == "__main__":
    main()
