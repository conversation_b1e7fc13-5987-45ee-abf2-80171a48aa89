<BoundClasses.BoundUnsqueeze object at 0x7fbccc4e5300>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc4e5270>
<BoundClasses.BoundCast object at 0x7fbccc4e51e0>
<BoundClasses.BoundConstant object at 0x7fbccc4e56f0>
<BoundClasses.BoundSub object at 0x7fbccc4e5330>
<BoundClasses.BoundConstant object at 0x7fbccc4e5390>
<BoundClasses.BoundMul object at 0x7fbccc4e53f0>
torch.Size([1, 1, 1, 8]) torch.Size([1])
<BoundClasses.BoundShape object at 0x7fbccc4e56c0>
<BoundClasses.BoundConstant object at 0x7fbccc4e5570>
<BoundClasses.BoundGather object at 0x7fbccc4e54e0>
<BoundClasses.BoundConstant object at 0x7fbccc4e55d0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc4e5630>
<BoundClasses.BoundConstant object at 0x7fbccc4e7d90>
<BoundClasses.BoundConstant object at 0x7fbccc4e5720>
<BoundClasses.BoundSlice object at 0x7fbccc4e6110>
<BoundClasses.BoundGather object at 0x7fbccc4e5810>
<BoundClasses.BoundGather object at 0x7fbccc4e5e70>
<BoundClasses.BoundAdd object at 0x7fbccc4e5d80>
torch.Size([1, 8, 768]) torch.Size([1, 8, 768])
<BoundClasses.BoundGather object at 0x7fbccc4e6d10>
<BoundClasses.BoundAdd object at 0x7fbccc4e6170>
torch.Size([1, 8, 768]) torch.Size([1, 8, 768])
<BoundClasses.BoundReduceMean object at 0x7fbccc4e61d0>
<BoundClasses.BoundSub object at 0x7fbccc4e7dc0>
<BoundClasses.BoundConstant object at 0x7fbccc4e7d00>
<BoundClasses.BoundPow object at 0x7fbccc4e5ba0>
<BoundClasses.BoundReduceMean object at 0x7fbccc4e7bb0>
<BoundClasses.BoundConstant object at 0x7fbccc4e5c60>
<BoundClasses.BoundAdd object at 0x7fbccc4e5cc0>
torch.Size([1, 8, 1]) torch.Size([1])
<BoundClasses.BoundSqrt object at 0x7fbccc4e7880>
<BoundClasses.BoundDiv object at 0x7fbccc4e5d20>
<BoundClasses.BoundMul object at 0x7fbccc4e5e10>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundAdd object at 0x7fbccc4e7cd0>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundMatMul object at 0x7fbccc4e77c0>
<BoundClasses.BoundAdd object at 0x7fbccc4e51b0>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundMatMul object at 0x7fbccc4e7e80>
<BoundClasses.BoundAdd object at 0x7fbccc4e6f20>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundShape object at 0x7fbccc4e6ec0>
<BoundClasses.BoundConstant object at 0x7fbccc4e6dd0>
<BoundClasses.BoundGather object at 0x7fbccc4e6e60>
<BoundClasses.BoundShape object at 0x7fbccc4e6e00>
<BoundClasses.BoundConstant object at 0x7fbccc4e6c20>
<BoundClasses.BoundGather object at 0x7fbccc4e6c80>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc4e5210>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc4e5510>
<BoundClasses.BoundConstant object at 0x7fbccc4e7d30>
<BoundClasses.BoundConstant object at 0x7fbccc4e7b50>
<BoundClasses.BoundConcat object at 0x7fbccc4e73d0>
<BoundClasses.BoundReshape object at 0x7fbccc4e76d0>
<BoundClasses.BoundMatMul object at 0x7fbccc4e7670>
<BoundClasses.BoundAdd object at 0x7fbccc4e72b0>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundShape object at 0x7fbccc4e7310>
<BoundClasses.BoundConstant object at 0x7fbccc4e7220>
<BoundClasses.BoundGather object at 0x7fbccc4e7250>
<BoundClasses.BoundShape object at 0x7fbccc4e7160>
<BoundClasses.BoundConstant object at 0x7fbccc4e7100>
<BoundClasses.BoundGather object at 0x7fbccc4e7010>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc4e70a0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc4e7040>
<BoundClasses.BoundConstant object at 0x7fbccc4e6b00>
<BoundClasses.BoundConstant object at 0x7fbccc4e6b60>
<BoundClasses.BoundConcat object at 0x7fbccc4e69e0>
<BoundClasses.BoundReshape object at 0x7fbccc4e6a10>
<BoundClasses.BoundTranspose object at 0x7fbccc4e6980>
<BoundClasses.BoundShape object at 0x7fbccc4e68f0>
<BoundClasses.BoundConstant object at 0x7fbccc4e67d0>
<BoundClasses.BoundGather object at 0x7fbccc4e68c0>
<BoundClasses.BoundShape object at 0x7fbccc4e6800>
<BoundClasses.BoundConstant object at 0x7fbccc4e65f0>
<BoundClasses.BoundGather object at 0x7fbccc4e6650>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc4e5a20>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc4e5960>
<BoundClasses.BoundConstant object at 0x7fbccc4e5ab0>
<BoundClasses.BoundConstant object at 0x7fbccc4e7a90>
<BoundClasses.BoundConcat object at 0x7fbccc4e7700>
<BoundClasses.BoundReshape object at 0x7fbccc4e5a80>
<BoundClasses.BoundTranspose object at 0x7fbccc4e7940>
<BoundClasses.BoundTranspose object at 0x7fbccc4e58a0>
<BoundClasses.BoundMatMul object at 0x7fbd2dba3880>
<BoundClasses.BoundConstant object at 0x7fbd2d8f44f0>
<BoundClasses.BoundDiv object at 0x7fbd2da17580>
<BoundClasses.BoundAdd object at 0x7fbd2da144f0>
torch.Size([1, 12, 8, 8]) torch.Size([1, 1, 1, 8])
<BoundClasses.BoundSoftmax object at 0x7fbd2d7f5870>
<BoundClasses.BoundMatMul object at 0x7fbd2d7f5a80>
<BoundClasses.BoundTranspose object at 0x7fbd2d7f56c0>
<BoundClasses.BoundShape object at 0x7fbd2d7f5510>
<BoundClasses.BoundConstant object at 0x7fbccc380d60>
<BoundClasses.BoundGather object at 0x7fbccc380dc0>
<BoundClasses.BoundShape object at 0x7fbccc380cd0>
<BoundClasses.BoundConstant object at 0x7fbccc380d00>
<BoundClasses.BoundGather object at 0x7fbccc380c10>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc380bb0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc380ac0>
<BoundClasses.BoundConstant object at 0x7fbccc380b50>
<BoundClasses.BoundConcat object at 0x7fbccc380af0>
<BoundClasses.BoundReshape object at 0x7fbccc3809a0>
<BoundClasses.BoundMatMul object at 0x7fbccc380a00>
<BoundClasses.BoundAdd object at 0x7fbccc380910>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundAdd object at 0x7fbccc380940>
torch.Size([1, 8, 768]) torch.Size([1, 8, 768])
<BoundClasses.BoundReduceMean object at 0x7fbccc380850>
<BoundClasses.BoundSub object at 0x7fbccc3807f0>
<BoundClasses.BoundConstant object at 0x7fbccc380700>
<BoundClasses.BoundPow object at 0x7fbccc380790>
<BoundClasses.BoundReduceMean object at 0x7fbccc380730>
<BoundClasses.BoundConstant object at 0x7fbccc3805e0>
<BoundClasses.BoundAdd object at 0x7fbccc380640>
torch.Size([1, 8, 1]) torch.Size([1])
<BoundClasses.BoundSqrt object at 0x7fbccc380550>
<BoundClasses.BoundDiv object at 0x7fbccc380580>
<BoundClasses.BoundMul object at 0x7fbccc380490>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundAdd object at 0x7fbccc380430>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundMatMul object at 0x7fbccc380340>
<BoundClasses.BoundAdd object at 0x7fbccc3803d0>
torch.Size([3072]) torch.Size([1, 8, 3072])
<BoundClasses.BoundConstant object at 0x7fbccc380370>
<BoundClasses.BoundDiv object at 0x7fbccc3802b0>
<BoundClasses.BoundErf object at 0x7fbccc380250>
<BoundClasses.BoundConstant object at 0x7fbccc3801f0>
<BoundClasses.BoundAdd object at 0x7fbccc3800d0>
torch.Size([1, 8, 3072]) torch.Size([1])
<BoundClasses.BoundMul object at 0x7fbccc380070>
torch.Size([1, 8, 3072]) torch.Size([1, 8, 3072])
<BoundClasses.BoundConstant object at 0x7fbccc380040>
<BoundClasses.BoundMul object at 0x7fbccc3810f0>
torch.Size([1, 8, 3072]) torch.Size([1])
<BoundClasses.BoundMatMul object at 0x7fbccc381090>
<BoundClasses.BoundAdd object at 0x7fbccc381030>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundAdd object at 0x7fbccc380fd0>
torch.Size([1, 8, 768]) torch.Size([1, 8, 768])
<BoundClasses.BoundReduceMean object at 0x7fbccc380f70>
<BoundClasses.BoundSub object at 0x7fbccc380f10>
<BoundClasses.BoundConstant object at 0x7fbccc380eb0>
<BoundClasses.BoundPow object at 0x7fbccc381210>
<BoundClasses.BoundReduceMean object at 0x7fbccc381300>
<BoundClasses.BoundConstant object at 0x7fbccc3812a0>
<BoundClasses.BoundAdd object at 0x7fbccc3813c0>
torch.Size([1, 8, 1]) torch.Size([1])
<BoundClasses.BoundSqrt object at 0x7fbccc3812d0>
<BoundClasses.BoundDiv object at 0x7fbccc381270>
<BoundClasses.BoundMul object at 0x7fbccc381420>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundAdd object at 0x7fbccc381480>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundMatMul object at 0x7fbccc381150>
<BoundClasses.BoundAdd object at 0x7fbccc380e20>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundMatMul object at 0x7fbccc381510>
<BoundClasses.BoundAdd object at 0x7fbccc381570>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundShape object at 0x7fbccc3815d0>
<BoundClasses.BoundConstant object at 0x7fbccc381630>
<BoundClasses.BoundGather object at 0x7fbccc381690>
<BoundClasses.BoundShape object at 0x7fbccc3816f0>
<BoundClasses.BoundConstant object at 0x7fbccc381750>
<BoundClasses.BoundGather object at 0x7fbccc3817b0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc381810>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc381870>
<BoundClasses.BoundConstant object at 0x7fbccc3818d0>
<BoundClasses.BoundConstant object at 0x7fbccc381930>
<BoundClasses.BoundConcat object at 0x7fbccc381990>
<BoundClasses.BoundReshape object at 0x7fbccc3819f0>
<BoundClasses.BoundMatMul object at 0x7fbccc381a50>
<BoundClasses.BoundAdd object at 0x7fbccc381ab0>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundShape object at 0x7fbccc381b10>
<BoundClasses.BoundConstant object at 0x7fbccc381b70>
<BoundClasses.BoundGather object at 0x7fbccc381bd0>
<BoundClasses.BoundShape object at 0x7fbccc381c30>
<BoundClasses.BoundConstant object at 0x7fbccc381c90>
<BoundClasses.BoundGather object at 0x7fbccc381cf0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc381d50>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc381db0>
<BoundClasses.BoundConstant object at 0x7fbccc381e10>
<BoundClasses.BoundConstant object at 0x7fbccc381e70>
<BoundClasses.BoundConcat object at 0x7fbccc381ed0>
<BoundClasses.BoundReshape object at 0x7fbccc381f30>
<BoundClasses.BoundTranspose object at 0x7fbccc381f90>
<BoundClasses.BoundShape object at 0x7fbccc381ff0>
<BoundClasses.BoundConstant object at 0x7fbccc382050>
<BoundClasses.BoundGather object at 0x7fbccc3820b0>
<BoundClasses.BoundShape object at 0x7fbccc382110>
<BoundClasses.BoundConstant object at 0x7fbccc382170>
<BoundClasses.BoundGather object at 0x7fbccc3821d0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc382230>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc382290>
<BoundClasses.BoundConstant object at 0x7fbccc3822f0>
<BoundClasses.BoundConstant object at 0x7fbccc382350>
<BoundClasses.BoundConcat object at 0x7fbccc3823b0>
<BoundClasses.BoundReshape object at 0x7fbccc382410>
<BoundClasses.BoundTranspose object at 0x7fbccc382470>
<BoundClasses.BoundTranspose object at 0x7fbccc3824d0>
<BoundClasses.BoundMatMul object at 0x7fbccc382530>
<BoundClasses.BoundConstant object at 0x7fbccc382590>
<BoundClasses.BoundDiv object at 0x7fbccc3825f0>
<BoundClasses.BoundAdd object at 0x7fbccc382650>
torch.Size([1, 12, 8, 8]) torch.Size([1, 1, 1, 8])
<BoundClasses.BoundSoftmax object at 0x7fbccc3826b0>
<BoundClasses.BoundMatMul object at 0x7fbccc382710>
<BoundClasses.BoundTranspose object at 0x7fbccc382770>
<BoundClasses.BoundShape object at 0x7fbccc3827d0>
<BoundClasses.BoundConstant object at 0x7fbccc382830>
<BoundClasses.BoundGather object at 0x7fbccc382890>
<BoundClasses.BoundShape object at 0x7fbccc3828f0>
<BoundClasses.BoundConstant object at 0x7fbccc382950>
<BoundClasses.BoundGather object at 0x7fbccc3829b0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc382a10>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc382a70>
<BoundClasses.BoundConstant object at 0x7fbccc382ad0>
<BoundClasses.BoundConcat object at 0x7fbccc382b30>
<BoundClasses.BoundReshape object at 0x7fbccc382b90>
<BoundClasses.BoundMatMul object at 0x7fbccc382bf0>
<BoundClasses.BoundAdd object at 0x7fbccc382c50>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundAdd object at 0x7fbccc382cb0>
torch.Size([1, 8, 768]) torch.Size([1, 8, 768])
<BoundClasses.BoundReduceMean object at 0x7fbccc382d10>
<BoundClasses.BoundSub object at 0x7fbccc382d70>
<BoundClasses.BoundConstant object at 0x7fbccc382dd0>
<BoundClasses.BoundPow object at 0x7fbccc382e30>
<BoundClasses.BoundReduceMean object at 0x7fbccc382e90>
<BoundClasses.BoundConstant object at 0x7fbccc382ef0>
<BoundClasses.BoundAdd object at 0x7fbccc382f50>
torch.Size([1, 8, 1]) torch.Size([1])
<BoundClasses.BoundSqrt object at 0x7fbccc382fb0>
<BoundClasses.BoundDiv object at 0x7fbccc383010>
<BoundClasses.BoundMul object at 0x7fbccc383070>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundAdd object at 0x7fbccc3830d0>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundMatMul object at 0x7fbccc383130>
<BoundClasses.BoundAdd object at 0x7fbccc383190>
torch.Size([3072]) torch.Size([1, 8, 3072])
<BoundClasses.BoundConstant object at 0x7fbccc3831f0>
<BoundClasses.BoundDiv object at 0x7fbccc383250>
<BoundClasses.BoundErf object at 0x7fbccc3832b0>
<BoundClasses.BoundConstant object at 0x7fbccc383310>
<BoundClasses.BoundAdd object at 0x7fbccc383370>
torch.Size([1, 8, 3072]) torch.Size([1])
<BoundClasses.BoundMul object at 0x7fbccc3833d0>
torch.Size([1, 8, 3072]) torch.Size([1, 8, 3072])
<BoundClasses.BoundConstant object at 0x7fbccc383430>
<BoundClasses.BoundMul object at 0x7fbccc383490>
torch.Size([1, 8, 3072]) torch.Size([1])
<BoundClasses.BoundMatMul object at 0x7fbccc3834f0>
<BoundClasses.BoundAdd object at 0x7fbccc383550>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundAdd object at 0x7fbccc3835b0>
torch.Size([1, 8, 768]) torch.Size([1, 8, 768])
<BoundClasses.BoundReduceMean object at 0x7fbccc383610>
<BoundClasses.BoundSub object at 0x7fbccc383670>
<BoundClasses.BoundConstant object at 0x7fbccc3836d0>
<BoundClasses.BoundPow object at 0x7fbccc383730>
<BoundClasses.BoundReduceMean object at 0x7fbccc383790>
<BoundClasses.BoundConstant object at 0x7fbccc3837f0>
<BoundClasses.BoundAdd object at 0x7fbccc383850>
torch.Size([1, 8, 1]) torch.Size([1])
<BoundClasses.BoundSqrt object at 0x7fbccc3838b0>
<BoundClasses.BoundDiv object at 0x7fbccc383910>
<BoundClasses.BoundMul object at 0x7fbccc383970>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundAdd object at 0x7fbccc3839d0>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundMatMul object at 0x7fbccc383a30>
<BoundClasses.BoundAdd object at 0x7fbccc383a90>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundMatMul object at 0x7fbccc383af0>
<BoundClasses.BoundAdd object at 0x7fbccc383b50>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundShape object at 0x7fbccc383bb0>
<BoundClasses.BoundConstant object at 0x7fbccc383c10>
<BoundClasses.BoundGather object at 0x7fbccc383c70>
<BoundClasses.BoundShape object at 0x7fbccc383cd0>
<BoundClasses.BoundConstant object at 0x7fbccc383d30>
<BoundClasses.BoundGather object at 0x7fbccc383d90>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc383df0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc383e50>
<BoundClasses.BoundConstant object at 0x7fbccc383eb0>
<BoundClasses.BoundConstant object at 0x7fbccc383f10>
<BoundClasses.BoundConcat object at 0x7fbccc383f70>
<BoundClasses.BoundReshape object at 0x7fbccc383fd0>
<BoundClasses.BoundMatMul object at 0x7fbccc381180>
<BoundClasses.BoundAdd object at 0x7fbccc39c0d0>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundShape object at 0x7fbccc39c130>
<BoundClasses.BoundConstant object at 0x7fbccc39c190>
<BoundClasses.BoundGather object at 0x7fbccc39c1f0>
<BoundClasses.BoundShape object at 0x7fbccc39c250>
<BoundClasses.BoundConstant object at 0x7fbccc39c2b0>
<BoundClasses.BoundGather object at 0x7fbccc39c310>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc39c370>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc39c3d0>
<BoundClasses.BoundConstant object at 0x7fbccc39c430>
<BoundClasses.BoundConstant object at 0x7fbccc39c490>
<BoundClasses.BoundConcat object at 0x7fbccc39c4f0>
<BoundClasses.BoundReshape object at 0x7fbccc39c550>
<BoundClasses.BoundTranspose object at 0x7fbccc39c5b0>
<BoundClasses.BoundShape object at 0x7fbccc39c610>
<BoundClasses.BoundConstant object at 0x7fbccc39c670>
<BoundClasses.BoundGather object at 0x7fbccc39c6d0>
<BoundClasses.BoundShape object at 0x7fbccc39c730>
<BoundClasses.BoundConstant object at 0x7fbccc39c790>
<BoundClasses.BoundGather object at 0x7fbccc39c7f0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc39c850>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc39c8b0>
<BoundClasses.BoundConstant object at 0x7fbccc39c910>
<BoundClasses.BoundConstant object at 0x7fbccc39c970>
<BoundClasses.BoundConcat object at 0x7fbccc39c9d0>
<BoundClasses.BoundReshape object at 0x7fbccc39ca30>
<BoundClasses.BoundTranspose object at 0x7fbccc39ca90>
<BoundClasses.BoundTranspose object at 0x7fbccc39caf0>
<BoundClasses.BoundMatMul object at 0x7fbccc39cb50>
<BoundClasses.BoundConstant object at 0x7fbccc39cbb0>
<BoundClasses.BoundDiv object at 0x7fbccc39cc10>
<BoundClasses.BoundAdd object at 0x7fbccc39cc70>
torch.Size([1, 12, 8, 8]) torch.Size([1, 1, 1, 8])
<BoundClasses.BoundSoftmax object at 0x7fbccc39ccd0>
<BoundClasses.BoundMatMul object at 0x7fbccc39cd30>
<BoundClasses.BoundTranspose object at 0x7fbccc39cd90>
<BoundClasses.BoundShape object at 0x7fbccc39cdf0>
<BoundClasses.BoundConstant object at 0x7fbccc39ce50>
<BoundClasses.BoundGather object at 0x7fbccc39ceb0>
<BoundClasses.BoundShape object at 0x7fbccc39cf10>
<BoundClasses.BoundConstant object at 0x7fbccc39cf70>
<BoundClasses.BoundGather object at 0x7fbccc39cfd0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc39d030>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc39d090>
<BoundClasses.BoundConstant object at 0x7fbccc39d0f0>
<BoundClasses.BoundConcat object at 0x7fbccc39d150>
<BoundClasses.BoundReshape object at 0x7fbccc39d1b0>
<BoundClasses.BoundMatMul object at 0x7fbccc39d210>
<BoundClasses.BoundAdd object at 0x7fbccc39d270>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundAdd object at 0x7fbccc39d2d0>
torch.Size([1, 8, 768]) torch.Size([1, 8, 768])
<BoundClasses.BoundReduceMean object at 0x7fbccc39d330>
<BoundClasses.BoundSub object at 0x7fbccc39d390>
<BoundClasses.BoundConstant object at 0x7fbccc39d3f0>
<BoundClasses.BoundPow object at 0x7fbccc39d450>
<BoundClasses.BoundReduceMean object at 0x7fbccc39d4b0>
<BoundClasses.BoundConstant object at 0x7fbccc39d510>
<BoundClasses.BoundAdd object at 0x7fbccc39d570>
torch.Size([1, 8, 1]) torch.Size([1])
<BoundClasses.BoundSqrt object at 0x7fbccc39d5d0>
<BoundClasses.BoundDiv object at 0x7fbccc39d630>
<BoundClasses.BoundMul object at 0x7fbccc39d690>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundAdd object at 0x7fbccc39d6f0>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundMatMul object at 0x7fbccc39d750>
<BoundClasses.BoundAdd object at 0x7fbccc39d7b0>
torch.Size([3072]) torch.Size([1, 8, 3072])
<BoundClasses.BoundConstant object at 0x7fbccc39d810>
<BoundClasses.BoundDiv object at 0x7fbccc39d870>
<BoundClasses.BoundErf object at 0x7fbccc39d8d0>
<BoundClasses.BoundConstant object at 0x7fbccc39d930>
<BoundClasses.BoundAdd object at 0x7fbccc39d990>
torch.Size([1, 8, 3072]) torch.Size([1])
<BoundClasses.BoundMul object at 0x7fbccc39d9f0>
torch.Size([1, 8, 3072]) torch.Size([1, 8, 3072])
<BoundClasses.BoundConstant object at 0x7fbccc39da50>
<BoundClasses.BoundMul object at 0x7fbccc39dab0>
torch.Size([1, 8, 3072]) torch.Size([1])
<BoundClasses.BoundMatMul object at 0x7fbccc39db10>
<BoundClasses.BoundAdd object at 0x7fbccc39db70>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundAdd object at 0x7fbccc39dbd0>
torch.Size([1, 8, 768]) torch.Size([1, 8, 768])
<BoundClasses.BoundReduceMean object at 0x7fbccc39dc30>
<BoundClasses.BoundSub object at 0x7fbccc39dc90>
<BoundClasses.BoundConstant object at 0x7fbccc39dcf0>
<BoundClasses.BoundPow object at 0x7fbccc39dd50>
<BoundClasses.BoundReduceMean object at 0x7fbccc39ddb0>
<BoundClasses.BoundConstant object at 0x7fbccc39de10>
<BoundClasses.BoundAdd object at 0x7fbccc39de70>
torch.Size([1, 8, 1]) torch.Size([1])
<BoundClasses.BoundSqrt object at 0x7fbccc39ded0>
<BoundClasses.BoundDiv object at 0x7fbccc39df30>
<BoundClasses.BoundMul object at 0x7fbccc39df90>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundAdd object at 0x7fbccc39dff0>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundMatMul object at 0x7fbccc39e050>
<BoundClasses.BoundAdd object at 0x7fbccc39e0b0>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundMatMul object at 0x7fbccc39e110>
<BoundClasses.BoundAdd object at 0x7fbccc39e170>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundShape object at 0x7fbccc39e1d0>
<BoundClasses.BoundConstant object at 0x7fbccc39e230>
<BoundClasses.BoundGather object at 0x7fbccc39e290>
<BoundClasses.BoundShape object at 0x7fbccc39e2f0>
<BoundClasses.BoundConstant object at 0x7fbccc39e350>
<BoundClasses.BoundGather object at 0x7fbccc39e3b0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc39e410>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc39e470>
<BoundClasses.BoundConstant object at 0x7fbccc39e4d0>
<BoundClasses.BoundConstant object at 0x7fbccc39e530>
<BoundClasses.BoundConcat object at 0x7fbccc39e590>
<BoundClasses.BoundReshape object at 0x7fbccc39e5f0>
<BoundClasses.BoundMatMul object at 0x7fbccc39e650>
<BoundClasses.BoundAdd object at 0x7fbccc39e6b0>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundShape object at 0x7fbccc39e710>
<BoundClasses.BoundConstant object at 0x7fbccc39e770>
<BoundClasses.BoundGather object at 0x7fbccc39e7d0>
<BoundClasses.BoundShape object at 0x7fbccc39e830>
<BoundClasses.BoundConstant object at 0x7fbccc39e890>
<BoundClasses.BoundGather object at 0x7fbccc39e8f0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc39e950>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc39e9b0>
<BoundClasses.BoundConstant object at 0x7fbccc39ea10>
<BoundClasses.BoundConstant object at 0x7fbccc39ea70>
<BoundClasses.BoundConcat object at 0x7fbccc39ead0>
<BoundClasses.BoundReshape object at 0x7fbccc39eb30>
<BoundClasses.BoundTranspose object at 0x7fbccc39eb90>
<BoundClasses.BoundShape object at 0x7fbccc39ebf0>
<BoundClasses.BoundConstant object at 0x7fbccc39ec50>
<BoundClasses.BoundGather object at 0x7fbccc39ecb0>
<BoundClasses.BoundShape object at 0x7fbccc39ed10>
<BoundClasses.BoundConstant object at 0x7fbccc39ed70>
<BoundClasses.BoundGather object at 0x7fbccc39edd0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc39ee30>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc39ee90>
<BoundClasses.BoundConstant object at 0x7fbccc39eef0>
<BoundClasses.BoundConstant object at 0x7fbccc39ef50>
<BoundClasses.BoundConcat object at 0x7fbccc39efb0>
<BoundClasses.BoundReshape object at 0x7fbccc39f010>
<BoundClasses.BoundTranspose object at 0x7fbccc39f070>
<BoundClasses.BoundTranspose object at 0x7fbccc39f0d0>
<BoundClasses.BoundMatMul object at 0x7fbccc39f130>
<BoundClasses.BoundConstant object at 0x7fbccc39f190>
<BoundClasses.BoundDiv object at 0x7fbccc39f1f0>
<BoundClasses.BoundAdd object at 0x7fbccc39f250>
torch.Size([1, 12, 8, 8]) torch.Size([1, 1, 1, 8])
<BoundClasses.BoundSoftmax object at 0x7fbccc39f2b0>
<BoundClasses.BoundMatMul object at 0x7fbccc39f310>
<BoundClasses.BoundTranspose object at 0x7fbccc39f370>
<BoundClasses.BoundShape object at 0x7fbccc39f3d0>
<BoundClasses.BoundConstant object at 0x7fbccc39f430>
<BoundClasses.BoundGather object at 0x7fbccc39f490>
<BoundClasses.BoundShape object at 0x7fbccc39f4f0>
<BoundClasses.BoundConstant object at 0x7fbccc39f550>
<BoundClasses.BoundGather object at 0x7fbccc39f5b0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc39f610>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc39f670>
<BoundClasses.BoundConstant object at 0x7fbccc39f6d0>
<BoundClasses.BoundConcat object at 0x7fbccc39f730>
<BoundClasses.BoundReshape object at 0x7fbccc39f790>
<BoundClasses.BoundMatMul object at 0x7fbccc39f7f0>
<BoundClasses.BoundAdd object at 0x7fbccc39f850>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundAdd object at 0x7fbccc39f8b0>
torch.Size([1, 8, 768]) torch.Size([1, 8, 768])
<BoundClasses.BoundReduceMean object at 0x7fbccc39f910>
<BoundClasses.BoundSub object at 0x7fbccc39f970>
<BoundClasses.BoundConstant object at 0x7fbccc39f9d0>
<BoundClasses.BoundPow object at 0x7fbccc39fa30>
<BoundClasses.BoundReduceMean object at 0x7fbccc39fa90>
<BoundClasses.BoundConstant object at 0x7fbccc39faf0>
<BoundClasses.BoundAdd object at 0x7fbccc39fb50>
torch.Size([1, 8, 1]) torch.Size([1])
<BoundClasses.BoundSqrt object at 0x7fbccc39fbb0>
<BoundClasses.BoundDiv object at 0x7fbccc39fc10>
<BoundClasses.BoundMul object at 0x7fbccc39fc70>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundAdd object at 0x7fbccc39fcd0>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundMatMul object at 0x7fbccc39fd30>
<BoundClasses.BoundAdd object at 0x7fbccc39fd90>
torch.Size([3072]) torch.Size([1, 8, 3072])
<BoundClasses.BoundConstant object at 0x7fbccc39fdf0>
<BoundClasses.BoundDiv object at 0x7fbccc39fe50>
<BoundClasses.BoundErf object at 0x7fbccc39feb0>
<BoundClasses.BoundConstant object at 0x7fbccc39ff10>
<BoundClasses.BoundAdd object at 0x7fbccc39ff70>
torch.Size([1, 8, 3072]) torch.Size([1])
<BoundClasses.BoundMul object at 0x7fbccc39ffd0>
torch.Size([1, 8, 3072]) torch.Size([1, 8, 3072])
<BoundClasses.BoundConstant object at 0x7fbccc39c070>
<BoundClasses.BoundMul object at 0x7fbccc37c0d0>
torch.Size([1, 8, 3072]) torch.Size([1])
<BoundClasses.BoundMatMul object at 0x7fbccc37c130>
<BoundClasses.BoundAdd object at 0x7fbccc37c190>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundAdd object at 0x7fbccc37c1f0>
torch.Size([1, 8, 768]) torch.Size([1, 8, 768])
<BoundClasses.BoundReduceMean object at 0x7fbccc37c250>
<BoundClasses.BoundSub object at 0x7fbccc37c2b0>
<BoundClasses.BoundConstant object at 0x7fbccc37c310>
<BoundClasses.BoundPow object at 0x7fbccc37c370>
<BoundClasses.BoundReduceMean object at 0x7fbccc37c3d0>
<BoundClasses.BoundConstant object at 0x7fbccc37c430>
<BoundClasses.BoundAdd object at 0x7fbccc37c490>
torch.Size([1, 8, 1]) torch.Size([1])
<BoundClasses.BoundSqrt object at 0x7fbccc37c4f0>
<BoundClasses.BoundDiv object at 0x7fbccc37c550>
<BoundClasses.BoundMul object at 0x7fbccc37c5b0>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundAdd object at 0x7fbccc37c610>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundMatMul object at 0x7fbccc37c670>
<BoundClasses.BoundAdd object at 0x7fbccc37c6d0>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundMatMul object at 0x7fbccc37c730>
<BoundClasses.BoundAdd object at 0x7fbccc37c790>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundShape object at 0x7fbccc37c7f0>
<BoundClasses.BoundConstant object at 0x7fbccc37c850>
<BoundClasses.BoundGather object at 0x7fbccc37c8b0>
<BoundClasses.BoundShape object at 0x7fbccc37c910>
<BoundClasses.BoundConstant object at 0x7fbccc37c970>
<BoundClasses.BoundGather object at 0x7fbccc37c9d0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc37ca30>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc37ca90>
<BoundClasses.BoundConstant object at 0x7fbccc37caf0>
<BoundClasses.BoundConstant object at 0x7fbccc37cb50>
<BoundClasses.BoundConcat object at 0x7fbccc37cbb0>
<BoundClasses.BoundReshape object at 0x7fbccc37cc10>
<BoundClasses.BoundMatMul object at 0x7fbccc37cc70>
<BoundClasses.BoundAdd object at 0x7fbccc37ccd0>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundShape object at 0x7fbccc37cd30>
<BoundClasses.BoundConstant object at 0x7fbccc37cd90>
<BoundClasses.BoundGather object at 0x7fbccc37cdf0>
<BoundClasses.BoundShape object at 0x7fbccc37ce50>
<BoundClasses.BoundConstant object at 0x7fbccc37ceb0>
<BoundClasses.BoundGather object at 0x7fbccc37cf10>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc37cf70>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc37cfd0>
<BoundClasses.BoundConstant object at 0x7fbccc37d030>
<BoundClasses.BoundConstant object at 0x7fbccc37d090>
<BoundClasses.BoundConcat object at 0x7fbccc37d0f0>
<BoundClasses.BoundReshape object at 0x7fbccc37d150>
<BoundClasses.BoundTranspose object at 0x7fbccc37d1b0>
<BoundClasses.BoundShape object at 0x7fbccc37d210>
<BoundClasses.BoundConstant object at 0x7fbccc37d270>
<BoundClasses.BoundGather object at 0x7fbccc37d2d0>
<BoundClasses.BoundShape object at 0x7fbccc37d330>
<BoundClasses.BoundConstant object at 0x7fbccc37d390>
<BoundClasses.BoundGather object at 0x7fbccc37d3f0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc37d450>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc37d4b0>
<BoundClasses.BoundConstant object at 0x7fbccc37d510>
<BoundClasses.BoundConstant object at 0x7fbccc37d570>
<BoundClasses.BoundConcat object at 0x7fbccc37d5d0>
<BoundClasses.BoundReshape object at 0x7fbccc37d630>
<BoundClasses.BoundTranspose object at 0x7fbccc37d690>
<BoundClasses.BoundTranspose object at 0x7fbccc37d6f0>
<BoundClasses.BoundMatMul object at 0x7fbccc37d750>
<BoundClasses.BoundConstant object at 0x7fbccc37d7b0>
<BoundClasses.BoundDiv object at 0x7fbccc37d810>
<BoundClasses.BoundAdd object at 0x7fbccc37d870>
torch.Size([1, 12, 8, 8]) torch.Size([1, 1, 1, 8])
<BoundClasses.BoundSoftmax object at 0x7fbccc37d8d0>
<BoundClasses.BoundMatMul object at 0x7fbccc37d930>
<BoundClasses.BoundTranspose object at 0x7fbccc37d990>
<BoundClasses.BoundShape object at 0x7fbccc37d9f0>
<BoundClasses.BoundConstant object at 0x7fbccc37da50>
<BoundClasses.BoundGather object at 0x7fbccc37dab0>
<BoundClasses.BoundShape object at 0x7fbccc37db10>
<BoundClasses.BoundConstant object at 0x7fbccc37db70>
<BoundClasses.BoundGather object at 0x7fbccc37dbd0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc37dc30>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc37dc90>
<BoundClasses.BoundConstant object at 0x7fbccc37dcf0>
<BoundClasses.BoundConcat object at 0x7fbccc37dd50>
<BoundClasses.BoundReshape object at 0x7fbccc37ddb0>
<BoundClasses.BoundMatMul object at 0x7fbccc37de10>
<BoundClasses.BoundAdd object at 0x7fbccc37de70>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundAdd object at 0x7fbccc37ded0>
torch.Size([1, 8, 768]) torch.Size([1, 8, 768])
<BoundClasses.BoundReduceMean object at 0x7fbccc37df30>
<BoundClasses.BoundSub object at 0x7fbccc37df90>
<BoundClasses.BoundConstant object at 0x7fbccc37dff0>
<BoundClasses.BoundPow object at 0x7fbccc37e050>
<BoundClasses.BoundReduceMean object at 0x7fbccc37e0b0>
<BoundClasses.BoundConstant object at 0x7fbccc37e110>
<BoundClasses.BoundAdd object at 0x7fbccc37e170>
torch.Size([1, 8, 1]) torch.Size([1])
<BoundClasses.BoundSqrt object at 0x7fbccc37e1d0>
<BoundClasses.BoundDiv object at 0x7fbccc37e230>
<BoundClasses.BoundMul object at 0x7fbccc37e290>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundAdd object at 0x7fbccc37e2f0>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundMatMul object at 0x7fbccc37e350>
<BoundClasses.BoundAdd object at 0x7fbccc37e3b0>
torch.Size([3072]) torch.Size([1, 8, 3072])
<BoundClasses.BoundConstant object at 0x7fbccc37e410>
<BoundClasses.BoundDiv object at 0x7fbccc37e470>
<BoundClasses.BoundErf object at 0x7fbccc37e4d0>
<BoundClasses.BoundConstant object at 0x7fbccc37e530>
<BoundClasses.BoundAdd object at 0x7fbccc37e590>
torch.Size([1, 8, 3072]) torch.Size([1])
<BoundClasses.BoundMul object at 0x7fbccc37e5f0>
torch.Size([1, 8, 3072]) torch.Size([1, 8, 3072])
<BoundClasses.BoundConstant object at 0x7fbccc37e650>
<BoundClasses.BoundMul object at 0x7fbccc37e6b0>
torch.Size([1, 8, 3072]) torch.Size([1])
<BoundClasses.BoundMatMul object at 0x7fbccc37e710>
<BoundClasses.BoundAdd object at 0x7fbccc37e770>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundAdd object at 0x7fbccc37e7d0>
torch.Size([1, 8, 768]) torch.Size([1, 8, 768])
<BoundClasses.BoundReduceMean object at 0x7fbccc37e830>
<BoundClasses.BoundSub object at 0x7fbccc37e890>
<BoundClasses.BoundConstant object at 0x7fbccc37e8f0>
<BoundClasses.BoundPow object at 0x7fbccc37e950>
<BoundClasses.BoundReduceMean object at 0x7fbccc37e9b0>
<BoundClasses.BoundConstant object at 0x7fbccc37ea10>
<BoundClasses.BoundAdd object at 0x7fbccc37ea70>
torch.Size([1, 8, 1]) torch.Size([1])
<BoundClasses.BoundSqrt object at 0x7fbccc37ead0>
<BoundClasses.BoundDiv object at 0x7fbccc37eb30>
<BoundClasses.BoundMul object at 0x7fbccc37eb90>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundAdd object at 0x7fbccc37ebf0>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundMatMul object at 0x7fbccc37ec50>
<BoundClasses.BoundAdd object at 0x7fbccc37ecb0>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundMatMul object at 0x7fbccc37ed10>
<BoundClasses.BoundAdd object at 0x7fbccc37ed70>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundShape object at 0x7fbccc37edd0>
<BoundClasses.BoundConstant object at 0x7fbccc37ee30>
<BoundClasses.BoundGather object at 0x7fbccc37ee90>
<BoundClasses.BoundShape object at 0x7fbccc37eef0>
<BoundClasses.BoundConstant object at 0x7fbccc37ef50>
<BoundClasses.BoundGather object at 0x7fbccc37efb0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc37f010>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc37f070>
<BoundClasses.BoundConstant object at 0x7fbccc37f0d0>
<BoundClasses.BoundConstant object at 0x7fbccc37f130>
<BoundClasses.BoundConcat object at 0x7fbccc37f190>
<BoundClasses.BoundReshape object at 0x7fbccc37f1f0>
<BoundClasses.BoundMatMul object at 0x7fbccc37f250>
<BoundClasses.BoundAdd object at 0x7fbccc37f2b0>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundShape object at 0x7fbccc37f310>
<BoundClasses.BoundConstant object at 0x7fbccc37f370>
<BoundClasses.BoundGather object at 0x7fbccc37f3d0>
<BoundClasses.BoundShape object at 0x7fbccc37f430>
<BoundClasses.BoundConstant object at 0x7fbccc37f490>
<BoundClasses.BoundGather object at 0x7fbccc37f4f0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc37f550>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc37f5b0>
<BoundClasses.BoundConstant object at 0x7fbccc37f610>
<BoundClasses.BoundConstant object at 0x7fbccc37f670>
<BoundClasses.BoundConcat object at 0x7fbccc37f6d0>
<BoundClasses.BoundReshape object at 0x7fbccc37f730>
<BoundClasses.BoundTranspose object at 0x7fbccc37f790>
<BoundClasses.BoundShape object at 0x7fbccc37f7f0>
<BoundClasses.BoundConstant object at 0x7fbccc37f850>
<BoundClasses.BoundGather object at 0x7fbccc37f8b0>
<BoundClasses.BoundShape object at 0x7fbccc37f910>
<BoundClasses.BoundConstant object at 0x7fbccc37f970>
<BoundClasses.BoundGather object at 0x7fbccc37f9d0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc37fa30>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc37fa90>
<BoundClasses.BoundConstant object at 0x7fbccc37faf0>
<BoundClasses.BoundConstant object at 0x7fbccc37fb50>
<BoundClasses.BoundConcat object at 0x7fbccc37fbb0>
<BoundClasses.BoundReshape object at 0x7fbccc37fc10>
<BoundClasses.BoundTranspose object at 0x7fbccc37fc70>
<BoundClasses.BoundTranspose object at 0x7fbccc37fcd0>
<BoundClasses.BoundMatMul object at 0x7fbccc37fd30>
<BoundClasses.BoundConstant object at 0x7fbccc37fd90>
<BoundClasses.BoundDiv object at 0x7fbccc37fdf0>
<BoundClasses.BoundAdd object at 0x7fbccc37fe50>
torch.Size([1, 12, 8, 8]) torch.Size([1, 1, 1, 8])
<BoundClasses.BoundSoftmax object at 0x7fbccc37feb0>
<BoundClasses.BoundMatMul object at 0x7fbccc37ff10>
<BoundClasses.BoundTranspose object at 0x7fbccc37ff70>
<BoundClasses.BoundShape object at 0x7fbccc37ffd0>
<BoundClasses.BoundConstant object at 0x7fbccc37c070>
<BoundClasses.BoundGather object at 0x7fbccc3380d0>
<BoundClasses.BoundShape object at 0x7fbccc338130>
<BoundClasses.BoundConstant object at 0x7fbccc338190>
<BoundClasses.BoundGather object at 0x7fbccc3381f0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc338250>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc3382b0>
<BoundClasses.BoundConstant object at 0x7fbccc338310>
<BoundClasses.BoundConcat object at 0x7fbccc338370>
<BoundClasses.BoundReshape object at 0x7fbccc3383d0>
<BoundClasses.BoundMatMul object at 0x7fbccc338430>
<BoundClasses.BoundAdd object at 0x7fbccc338490>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundAdd object at 0x7fbccc3384f0>
torch.Size([1, 8, 768]) torch.Size([1, 8, 768])
<BoundClasses.BoundReduceMean object at 0x7fbccc338550>
<BoundClasses.BoundSub object at 0x7fbccc3385b0>
<BoundClasses.BoundConstant object at 0x7fbccc338610>
<BoundClasses.BoundPow object at 0x7fbccc338670>
<BoundClasses.BoundReduceMean object at 0x7fbccc3386d0>
<BoundClasses.BoundConstant object at 0x7fbccc338730>
<BoundClasses.BoundAdd object at 0x7fbccc338790>
torch.Size([1, 8, 1]) torch.Size([1])
<BoundClasses.BoundSqrt object at 0x7fbccc3387f0>
<BoundClasses.BoundDiv object at 0x7fbccc338850>
<BoundClasses.BoundMul object at 0x7fbccc3388b0>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundAdd object at 0x7fbccc338910>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundMatMul object at 0x7fbccc338970>
<BoundClasses.BoundAdd object at 0x7fbccc3389d0>
torch.Size([3072]) torch.Size([1, 8, 3072])
<BoundClasses.BoundConstant object at 0x7fbccc338a30>
<BoundClasses.BoundDiv object at 0x7fbccc338a90>
<BoundClasses.BoundErf object at 0x7fbccc338af0>
<BoundClasses.BoundConstant object at 0x7fbccc338b50>
<BoundClasses.BoundAdd object at 0x7fbccc338bb0>
torch.Size([1, 8, 3072]) torch.Size([1])
<BoundClasses.BoundMul object at 0x7fbccc338c10>
torch.Size([1, 8, 3072]) torch.Size([1, 8, 3072])
<BoundClasses.BoundConstant object at 0x7fbccc338c70>
<BoundClasses.BoundMul object at 0x7fbccc338cd0>
torch.Size([1, 8, 3072]) torch.Size([1])
<BoundClasses.BoundMatMul object at 0x7fbccc338d30>
<BoundClasses.BoundAdd object at 0x7fbccc338d90>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundAdd object at 0x7fbccc338df0>
torch.Size([1, 8, 768]) torch.Size([1, 8, 768])
<BoundClasses.BoundReduceMean object at 0x7fbccc338e50>
<BoundClasses.BoundSub object at 0x7fbccc338eb0>
<BoundClasses.BoundConstant object at 0x7fbccc338f10>
<BoundClasses.BoundPow object at 0x7fbccc338f70>
<BoundClasses.BoundReduceMean object at 0x7fbccc338fd0>
<BoundClasses.BoundConstant object at 0x7fbccc339030>
<BoundClasses.BoundAdd object at 0x7fbccc339090>
torch.Size([1, 8, 1]) torch.Size([1])
<BoundClasses.BoundSqrt object at 0x7fbccc3390f0>
<BoundClasses.BoundDiv object at 0x7fbccc339150>
<BoundClasses.BoundMul object at 0x7fbccc3391b0>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundAdd object at 0x7fbccc339210>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundMatMul object at 0x7fbccc339270>
<BoundClasses.BoundAdd object at 0x7fbccc3392d0>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundMatMul object at 0x7fbccc339330>
<BoundClasses.BoundAdd object at 0x7fbccc339390>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundShape object at 0x7fbccc3393f0>
<BoundClasses.BoundConstant object at 0x7fbccc339450>
<BoundClasses.BoundGather object at 0x7fbccc3394b0>
<BoundClasses.BoundShape object at 0x7fbccc339510>
<BoundClasses.BoundConstant object at 0x7fbccc339570>
<BoundClasses.BoundGather object at 0x7fbccc3395d0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc339630>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc339690>
<BoundClasses.BoundConstant object at 0x7fbccc3396f0>
<BoundClasses.BoundConstant object at 0x7fbccc339750>
<BoundClasses.BoundConcat object at 0x7fbccc3397b0>
<BoundClasses.BoundReshape object at 0x7fbccc339810>
<BoundClasses.BoundMatMul object at 0x7fbccc339870>
<BoundClasses.BoundAdd object at 0x7fbccc3398d0>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundShape object at 0x7fbccc339930>
<BoundClasses.BoundConstant object at 0x7fbccc339990>
<BoundClasses.BoundGather object at 0x7fbccc3399f0>
<BoundClasses.BoundShape object at 0x7fbccc339a50>
<BoundClasses.BoundConstant object at 0x7fbccc339ab0>
<BoundClasses.BoundGather object at 0x7fbccc339b10>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc339b70>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc339bd0>
<BoundClasses.BoundConstant object at 0x7fbccc339c30>
<BoundClasses.BoundConstant object at 0x7fbccc339c90>
<BoundClasses.BoundConcat object at 0x7fbccc339cf0>
<BoundClasses.BoundReshape object at 0x7fbccc339d50>
<BoundClasses.BoundTranspose object at 0x7fbccc339db0>
<BoundClasses.BoundShape object at 0x7fbccc339e10>
<BoundClasses.BoundConstant object at 0x7fbccc339e70>
<BoundClasses.BoundGather object at 0x7fbccc339ed0>
<BoundClasses.BoundShape object at 0x7fbccc339f30>
<BoundClasses.BoundConstant object at 0x7fbccc339f90>
<BoundClasses.BoundGather object at 0x7fbccc339ff0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc33a050>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc33a0b0>
<BoundClasses.BoundConstant object at 0x7fbccc33a110>
<BoundClasses.BoundConstant object at 0x7fbccc33a170>
<BoundClasses.BoundConcat object at 0x7fbccc33a1d0>
<BoundClasses.BoundReshape object at 0x7fbccc33a230>
<BoundClasses.BoundTranspose object at 0x7fbccc33a290>
<BoundClasses.BoundTranspose object at 0x7fbccc33a2f0>
<BoundClasses.BoundMatMul object at 0x7fbccc33a350>
<BoundClasses.BoundConstant object at 0x7fbccc33a3b0>
<BoundClasses.BoundDiv object at 0x7fbccc33a410>
<BoundClasses.BoundAdd object at 0x7fbccc33a470>
torch.Size([1, 12, 8, 8]) torch.Size([1, 1, 1, 8])
<BoundClasses.BoundSoftmax object at 0x7fbccc33a4d0>
<BoundClasses.BoundMatMul object at 0x7fbccc33a530>
<BoundClasses.BoundTranspose object at 0x7fbccc33a590>
<BoundClasses.BoundShape object at 0x7fbccc33a5f0>
<BoundClasses.BoundConstant object at 0x7fbccc33a650>
<BoundClasses.BoundGather object at 0x7fbccc33a6b0>
<BoundClasses.BoundShape object at 0x7fbccc33a710>
<BoundClasses.BoundConstant object at 0x7fbccc33a770>
<BoundClasses.BoundGather object at 0x7fbccc33a7d0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc33a830>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc33a890>
<BoundClasses.BoundConstant object at 0x7fbccc33a8f0>
<BoundClasses.BoundConcat object at 0x7fbccc33a950>
<BoundClasses.BoundReshape object at 0x7fbccc33a9b0>
<BoundClasses.BoundMatMul object at 0x7fbccc33aa10>
<BoundClasses.BoundAdd object at 0x7fbccc33aa70>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundAdd object at 0x7fbccc33aad0>
torch.Size([1, 8, 768]) torch.Size([1, 8, 768])
<BoundClasses.BoundReduceMean object at 0x7fbccc33ab30>
<BoundClasses.BoundSub object at 0x7fbccc33ab90>
<BoundClasses.BoundConstant object at 0x7fbccc33abf0>
<BoundClasses.BoundPow object at 0x7fbccc33ac50>
<BoundClasses.BoundReduceMean object at 0x7fbccc33acb0>
<BoundClasses.BoundConstant object at 0x7fbccc33ad10>
<BoundClasses.BoundAdd object at 0x7fbccc33ad70>
torch.Size([1, 8, 1]) torch.Size([1])
<BoundClasses.BoundSqrt object at 0x7fbccc33add0>
<BoundClasses.BoundDiv object at 0x7fbccc33ae30>
<BoundClasses.BoundMul object at 0x7fbccc33ae90>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundAdd object at 0x7fbccc33aef0>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundMatMul object at 0x7fbccc33af50>
<BoundClasses.BoundAdd object at 0x7fbccc33afb0>
torch.Size([3072]) torch.Size([1, 8, 3072])
<BoundClasses.BoundConstant object at 0x7fbccc33b010>
<BoundClasses.BoundDiv object at 0x7fbccc33b070>
<BoundClasses.BoundErf object at 0x7fbccc33b0d0>
<BoundClasses.BoundConstant object at 0x7fbccc33b130>
<BoundClasses.BoundAdd object at 0x7fbccc33b190>
torch.Size([1, 8, 3072]) torch.Size([1])
<BoundClasses.BoundMul object at 0x7fbccc33b1f0>
torch.Size([1, 8, 3072]) torch.Size([1, 8, 3072])
<BoundClasses.BoundConstant object at 0x7fbccc33b250>
<BoundClasses.BoundMul object at 0x7fbccc33b2b0>
torch.Size([1, 8, 3072]) torch.Size([1])
<BoundClasses.BoundMatMul object at 0x7fbccc33b310>
<BoundClasses.BoundAdd object at 0x7fbccc33b370>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundAdd object at 0x7fbccc33b3d0>
torch.Size([1, 8, 768]) torch.Size([1, 8, 768])
<BoundClasses.BoundReduceMean object at 0x7fbccc33b430>
<BoundClasses.BoundSub object at 0x7fbccc33b490>
<BoundClasses.BoundConstant object at 0x7fbccc33b4f0>
<BoundClasses.BoundPow object at 0x7fbccc33b550>
<BoundClasses.BoundReduceMean object at 0x7fbccc33b5b0>
<BoundClasses.BoundConstant object at 0x7fbccc33b610>
<BoundClasses.BoundAdd object at 0x7fbccc33b670>
torch.Size([1, 8, 1]) torch.Size([1])
<BoundClasses.BoundSqrt object at 0x7fbccc33b6d0>
<BoundClasses.BoundDiv object at 0x7fbccc33b730>
<BoundClasses.BoundMul object at 0x7fbccc33b790>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundAdd object at 0x7fbccc33b7f0>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundMatMul object at 0x7fbccc33b850>
<BoundClasses.BoundAdd object at 0x7fbccc33b8b0>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundMatMul object at 0x7fbccc33b910>
<BoundClasses.BoundAdd object at 0x7fbccc33b970>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundShape object at 0x7fbccc33b9d0>
<BoundClasses.BoundConstant object at 0x7fbccc33ba30>
<BoundClasses.BoundGather object at 0x7fbccc33ba90>
<BoundClasses.BoundShape object at 0x7fbccc33baf0>
<BoundClasses.BoundConstant object at 0x7fbccc33bb50>
<BoundClasses.BoundGather object at 0x7fbccc33bbb0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc33bc10>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc33bc70>
<BoundClasses.BoundConstant object at 0x7fbccc33bcd0>
<BoundClasses.BoundConstant object at 0x7fbccc33bd30>
<BoundClasses.BoundConcat object at 0x7fbccc33bd90>
<BoundClasses.BoundReshape object at 0x7fbccc33bdf0>
<BoundClasses.BoundMatMul object at 0x7fbccc33be50>
<BoundClasses.BoundAdd object at 0x7fbccc33beb0>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundShape object at 0x7fbccc33bf10>
<BoundClasses.BoundConstant object at 0x7fbccc33bf70>
<BoundClasses.BoundGather object at 0x7fbccc33bfd0>
<BoundClasses.BoundShape object at 0x7fbccc338070>
<BoundClasses.BoundConstant object at 0x7fbccc3e00d0>
<BoundClasses.BoundGather object at 0x7fbccc3e0130>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc3e0190>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc3e01f0>
<BoundClasses.BoundConstant object at 0x7fbccc3e0250>
<BoundClasses.BoundConstant object at 0x7fbccc3e02b0>
<BoundClasses.BoundConcat object at 0x7fbccc3e0310>
<BoundClasses.BoundReshape object at 0x7fbccc3e0370>
<BoundClasses.BoundTranspose object at 0x7fbccc3e03d0>
<BoundClasses.BoundShape object at 0x7fbccc3e0430>
<BoundClasses.BoundConstant object at 0x7fbccc3e0490>
<BoundClasses.BoundGather object at 0x7fbccc3e04f0>
<BoundClasses.BoundShape object at 0x7fbccc3e0550>
<BoundClasses.BoundConstant object at 0x7fbccc3e05b0>
<BoundClasses.BoundGather object at 0x7fbccc3e0610>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc3e0670>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc3e06d0>
<BoundClasses.BoundConstant object at 0x7fbccc3e0730>
<BoundClasses.BoundConstant object at 0x7fbccc3e0790>
<BoundClasses.BoundConcat object at 0x7fbccc3e07f0>
<BoundClasses.BoundReshape object at 0x7fbccc3e0850>
<BoundClasses.BoundTranspose object at 0x7fbccc3e08b0>
<BoundClasses.BoundTranspose object at 0x7fbccc3e0910>
<BoundClasses.BoundMatMul object at 0x7fbccc3e0970>
<BoundClasses.BoundConstant object at 0x7fbccc3e09d0>
<BoundClasses.BoundDiv object at 0x7fbccc3e0a30>
<BoundClasses.BoundAdd object at 0x7fbccc3e0a90>
torch.Size([1, 12, 8, 8]) torch.Size([1, 1, 1, 8])
<BoundClasses.BoundSoftmax object at 0x7fbccc3e0af0>
<BoundClasses.BoundMatMul object at 0x7fbccc3e0b50>
<BoundClasses.BoundTranspose object at 0x7fbccc3e0bb0>
<BoundClasses.BoundShape object at 0x7fbccc3e0c10>
<BoundClasses.BoundConstant object at 0x7fbccc3e0c70>
<BoundClasses.BoundGather object at 0x7fbccc3e0cd0>
<BoundClasses.BoundShape object at 0x7fbccc3e0d30>
<BoundClasses.BoundConstant object at 0x7fbccc3e0d90>
<BoundClasses.BoundGather object at 0x7fbccc3e0df0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc3e0e50>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc3e0eb0>
<BoundClasses.BoundConstant object at 0x7fbccc3e0f10>
<BoundClasses.BoundConcat object at 0x7fbccc3e0f70>
<BoundClasses.BoundReshape object at 0x7fbccc3e0fd0>
<BoundClasses.BoundMatMul object at 0x7fbccc3e1030>
<BoundClasses.BoundAdd object at 0x7fbccc3e1090>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundAdd object at 0x7fbccc3e10f0>
torch.Size([1, 8, 768]) torch.Size([1, 8, 768])
<BoundClasses.BoundReduceMean object at 0x7fbccc3e1150>
<BoundClasses.BoundSub object at 0x7fbccc3e11b0>
<BoundClasses.BoundConstant object at 0x7fbccc3e1210>
<BoundClasses.BoundPow object at 0x7fbccc3e1270>
<BoundClasses.BoundReduceMean object at 0x7fbccc3e12d0>
<BoundClasses.BoundConstant object at 0x7fbccc3e1330>
<BoundClasses.BoundAdd object at 0x7fbccc3e1390>
torch.Size([1, 8, 1]) torch.Size([1])
<BoundClasses.BoundSqrt object at 0x7fbccc3e13f0>
<BoundClasses.BoundDiv object at 0x7fbccc3e1450>
<BoundClasses.BoundMul object at 0x7fbccc3e14b0>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundAdd object at 0x7fbccc3e1510>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundMatMul object at 0x7fbccc3e1570>
<BoundClasses.BoundAdd object at 0x7fbccc3e15d0>
torch.Size([3072]) torch.Size([1, 8, 3072])
<BoundClasses.BoundConstant object at 0x7fbccc3e1630>
<BoundClasses.BoundDiv object at 0x7fbccc3e1690>
<BoundClasses.BoundErf object at 0x7fbccc3e16f0>
<BoundClasses.BoundConstant object at 0x7fbccc3e1750>
<BoundClasses.BoundAdd object at 0x7fbccc3e17b0>
torch.Size([1, 8, 3072]) torch.Size([1])
<BoundClasses.BoundMul object at 0x7fbccc3e1810>
torch.Size([1, 8, 3072]) torch.Size([1, 8, 3072])
<BoundClasses.BoundConstant object at 0x7fbccc3e1870>
<BoundClasses.BoundMul object at 0x7fbccc3e18d0>
torch.Size([1, 8, 3072]) torch.Size([1])
<BoundClasses.BoundMatMul object at 0x7fbccc3e1930>
<BoundClasses.BoundAdd object at 0x7fbccc3e1990>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundAdd object at 0x7fbccc3e19f0>
torch.Size([1, 8, 768]) torch.Size([1, 8, 768])
<BoundClasses.BoundReduceMean object at 0x7fbccc3e1a50>
<BoundClasses.BoundSub object at 0x7fbccc3e1ab0>
<BoundClasses.BoundConstant object at 0x7fbccc3e1b10>
<BoundClasses.BoundPow object at 0x7fbccc3e1b70>
<BoundClasses.BoundReduceMean object at 0x7fbccc3e1bd0>
<BoundClasses.BoundConstant object at 0x7fbccc3e1c30>
<BoundClasses.BoundAdd object at 0x7fbccc3e1c90>
torch.Size([1, 8, 1]) torch.Size([1])
<BoundClasses.BoundSqrt object at 0x7fbccc3e1cf0>
<BoundClasses.BoundDiv object at 0x7fbccc3e1d50>
<BoundClasses.BoundMul object at 0x7fbccc3e1db0>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundAdd object at 0x7fbccc3e1e10>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundMatMul object at 0x7fbccc3e1e70>
<BoundClasses.BoundAdd object at 0x7fbccc3e1ed0>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundMatMul object at 0x7fbccc3e1f30>
<BoundClasses.BoundAdd object at 0x7fbccc3e1f90>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundShape object at 0x7fbccc3e1ff0>
<BoundClasses.BoundConstant object at 0x7fbccc3e2050>
<BoundClasses.BoundGather object at 0x7fbccc3e20b0>
<BoundClasses.BoundShape object at 0x7fbccc3e2110>
<BoundClasses.BoundConstant object at 0x7fbccc3e2170>
<BoundClasses.BoundGather object at 0x7fbccc3e21d0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc3e2230>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc3e2290>
<BoundClasses.BoundConstant object at 0x7fbccc3e22f0>
<BoundClasses.BoundConstant object at 0x7fbccc3e2350>
<BoundClasses.BoundConcat object at 0x7fbccc3e23b0>
<BoundClasses.BoundReshape object at 0x7fbccc3e2410>
<BoundClasses.BoundMatMul object at 0x7fbccc3e2470>
<BoundClasses.BoundAdd object at 0x7fbccc3e24d0>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundShape object at 0x7fbccc3e2530>
<BoundClasses.BoundConstant object at 0x7fbccc3e2590>
<BoundClasses.BoundGather object at 0x7fbccc3e25f0>
<BoundClasses.BoundShape object at 0x7fbccc3e2650>
<BoundClasses.BoundConstant object at 0x7fbccc3e26b0>
<BoundClasses.BoundGather object at 0x7fbccc3e2710>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc3e2770>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc3e27d0>
<BoundClasses.BoundConstant object at 0x7fbccc3e2830>
<BoundClasses.BoundConstant object at 0x7fbccc3e2890>
<BoundClasses.BoundConcat object at 0x7fbccc3e28f0>
<BoundClasses.BoundReshape object at 0x7fbccc3e2950>
<BoundClasses.BoundTranspose object at 0x7fbccc3e29b0>
<BoundClasses.BoundShape object at 0x7fbccc3e2a10>
<BoundClasses.BoundConstant object at 0x7fbccc3e2a70>
<BoundClasses.BoundGather object at 0x7fbccc3e2ad0>
<BoundClasses.BoundShape object at 0x7fbccc3e2b30>
<BoundClasses.BoundConstant object at 0x7fbccc3e2b90>
<BoundClasses.BoundGather object at 0x7fbccc3e2bf0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc3e2c50>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc3e2cb0>
<BoundClasses.BoundConstant object at 0x7fbccc3e2d10>
<BoundClasses.BoundConstant object at 0x7fbccc3e2d70>
<BoundClasses.BoundConcat object at 0x7fbccc3e2dd0>
<BoundClasses.BoundReshape object at 0x7fbccc3e2e30>
<BoundClasses.BoundTranspose object at 0x7fbccc3e2e90>
<BoundClasses.BoundTranspose object at 0x7fbccc3e2ef0>
<BoundClasses.BoundMatMul object at 0x7fbccc3e2f50>
<BoundClasses.BoundConstant object at 0x7fbccc3e2fb0>
<BoundClasses.BoundDiv object at 0x7fbccc3e3010>
<BoundClasses.BoundAdd object at 0x7fbccc3e3070>
torch.Size([1, 12, 8, 8]) torch.Size([1, 1, 1, 8])
<BoundClasses.BoundSoftmax object at 0x7fbccc3e30d0>
<BoundClasses.BoundMatMul object at 0x7fbccc3e3130>
<BoundClasses.BoundTranspose object at 0x7fbccc3e3190>
<BoundClasses.BoundShape object at 0x7fbccc3e31f0>
<BoundClasses.BoundConstant object at 0x7fbccc3e3250>
<BoundClasses.BoundGather object at 0x7fbccc3e32b0>
<BoundClasses.BoundShape object at 0x7fbccc3e3310>
<BoundClasses.BoundConstant object at 0x7fbccc3e3370>
<BoundClasses.BoundGather object at 0x7fbccc3e33d0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc3e3430>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc3e3490>
<BoundClasses.BoundConstant object at 0x7fbccc3e34f0>
<BoundClasses.BoundConcat object at 0x7fbccc3e3550>
<BoundClasses.BoundReshape object at 0x7fbccc3e35b0>
<BoundClasses.BoundMatMul object at 0x7fbccc3e3610>
<BoundClasses.BoundAdd object at 0x7fbccc3e3670>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundAdd object at 0x7fbccc3e36d0>
torch.Size([1, 8, 768]) torch.Size([1, 8, 768])
<BoundClasses.BoundReduceMean object at 0x7fbccc3e3730>
<BoundClasses.BoundSub object at 0x7fbccc3e3790>
<BoundClasses.BoundConstant object at 0x7fbccc3e37f0>
<BoundClasses.BoundPow object at 0x7fbccc3e3850>
<BoundClasses.BoundReduceMean object at 0x7fbccc3e38b0>
<BoundClasses.BoundConstant object at 0x7fbccc3e3910>
<BoundClasses.BoundAdd object at 0x7fbccc3e3970>
torch.Size([1, 8, 1]) torch.Size([1])
<BoundClasses.BoundSqrt object at 0x7fbccc3e39d0>
<BoundClasses.BoundDiv object at 0x7fbccc3e3a30>
<BoundClasses.BoundMul object at 0x7fbccc3e3a90>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundAdd object at 0x7fbccc3e3af0>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundMatMul object at 0x7fbccc3e3b50>
<BoundClasses.BoundAdd object at 0x7fbccc3e3bb0>
torch.Size([3072]) torch.Size([1, 8, 3072])
<BoundClasses.BoundConstant object at 0x7fbccc3e3c10>
<BoundClasses.BoundDiv object at 0x7fbccc3e3c70>
<BoundClasses.BoundErf object at 0x7fbccc3e3cd0>
<BoundClasses.BoundConstant object at 0x7fbccc3e3d30>
<BoundClasses.BoundAdd object at 0x7fbccc3e3d90>
torch.Size([1, 8, 3072]) torch.Size([1])
<BoundClasses.BoundMul object at 0x7fbccc3e3df0>
torch.Size([1, 8, 3072]) torch.Size([1, 8, 3072])
<BoundClasses.BoundConstant object at 0x7fbccc3e3e50>
<BoundClasses.BoundMul object at 0x7fbccc3e3eb0>
torch.Size([1, 8, 3072]) torch.Size([1])
<BoundClasses.BoundMatMul object at 0x7fbccc3e3f10>
<BoundClasses.BoundAdd object at 0x7fbccc3e3f70>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundAdd object at 0x7fbccc3e3fd0>
torch.Size([1, 8, 768]) torch.Size([1, 8, 768])
<BoundClasses.BoundReduceMean object at 0x7fbccc3e0070>
<BoundClasses.BoundSub object at 0x7fbccc20c0d0>
<BoundClasses.BoundConstant object at 0x7fbccc20c130>
<BoundClasses.BoundPow object at 0x7fbccc20c190>
<BoundClasses.BoundReduceMean object at 0x7fbccc20c1f0>
<BoundClasses.BoundConstant object at 0x7fbccc20c250>
<BoundClasses.BoundAdd object at 0x7fbccc20c2b0>
torch.Size([1, 8, 1]) torch.Size([1])
<BoundClasses.BoundSqrt object at 0x7fbccc20c310>
<BoundClasses.BoundDiv object at 0x7fbccc20c370>
<BoundClasses.BoundMul object at 0x7fbccc20c3d0>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundAdd object at 0x7fbccc20c430>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundMatMul object at 0x7fbccc20c490>
<BoundClasses.BoundAdd object at 0x7fbccc20c4f0>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundMatMul object at 0x7fbccc20c550>
<BoundClasses.BoundAdd object at 0x7fbccc20c5b0>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundShape object at 0x7fbccc20c610>
<BoundClasses.BoundConstant object at 0x7fbccc20c670>
<BoundClasses.BoundGather object at 0x7fbccc20c6d0>
<BoundClasses.BoundShape object at 0x7fbccc20c730>
<BoundClasses.BoundConstant object at 0x7fbccc20c790>
<BoundClasses.BoundGather object at 0x7fbccc20c7f0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc20c850>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc20c8b0>
<BoundClasses.BoundConstant object at 0x7fbccc20c910>
<BoundClasses.BoundConstant object at 0x7fbccc20c970>
<BoundClasses.BoundConcat object at 0x7fbccc20c9d0>
<BoundClasses.BoundReshape object at 0x7fbccc20ca30>
<BoundClasses.BoundMatMul object at 0x7fbccc20ca90>
<BoundClasses.BoundAdd object at 0x7fbccc20caf0>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundShape object at 0x7fbccc20cb50>
<BoundClasses.BoundConstant object at 0x7fbccc20cbb0>
<BoundClasses.BoundGather object at 0x7fbccc20cc10>
<BoundClasses.BoundShape object at 0x7fbccc20cc70>
<BoundClasses.BoundConstant object at 0x7fbccc20ccd0>
<BoundClasses.BoundGather object at 0x7fbccc20cd30>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc20cd90>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc20cdf0>
<BoundClasses.BoundConstant object at 0x7fbccc20ce50>
<BoundClasses.BoundConstant object at 0x7fbccc20ceb0>
<BoundClasses.BoundConcat object at 0x7fbccc20cf10>
<BoundClasses.BoundReshape object at 0x7fbccc20cf70>
<BoundClasses.BoundTranspose object at 0x7fbccc20cfd0>
<BoundClasses.BoundShape object at 0x7fbccc20d030>
<BoundClasses.BoundConstant object at 0x7fbccc20d090>
<BoundClasses.BoundGather object at 0x7fbccc20d0f0>
<BoundClasses.BoundShape object at 0x7fbccc20d150>
<BoundClasses.BoundConstant object at 0x7fbccc20d1b0>
<BoundClasses.BoundGather object at 0x7fbccc20d210>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc20d270>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc20d2d0>
<BoundClasses.BoundConstant object at 0x7fbccc20d330>
<BoundClasses.BoundConstant object at 0x7fbccc20d390>
<BoundClasses.BoundConcat object at 0x7fbccc20d3f0>
<BoundClasses.BoundReshape object at 0x7fbccc20d450>
<BoundClasses.BoundTranspose object at 0x7fbccc20d4b0>
<BoundClasses.BoundTranspose object at 0x7fbccc20d510>
<BoundClasses.BoundMatMul object at 0x7fbccc20d570>
<BoundClasses.BoundConstant object at 0x7fbccc20d5d0>
<BoundClasses.BoundDiv object at 0x7fbccc20d630>
<BoundClasses.BoundAdd object at 0x7fbccc20d690>
torch.Size([1, 12, 8, 8]) torch.Size([1, 1, 1, 8])
<BoundClasses.BoundSoftmax object at 0x7fbccc20d6f0>
<BoundClasses.BoundMatMul object at 0x7fbccc20d750>
<BoundClasses.BoundTranspose object at 0x7fbccc20d7b0>
<BoundClasses.BoundShape object at 0x7fbccc20d810>
<BoundClasses.BoundConstant object at 0x7fbccc20d870>
<BoundClasses.BoundGather object at 0x7fbccc20d8d0>
<BoundClasses.BoundShape object at 0x7fbccc20d930>
<BoundClasses.BoundConstant object at 0x7fbccc20d990>
<BoundClasses.BoundGather object at 0x7fbccc20d9f0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc20da50>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc20dab0>
<BoundClasses.BoundConstant object at 0x7fbccc20db10>
<BoundClasses.BoundConcat object at 0x7fbccc20db70>
<BoundClasses.BoundReshape object at 0x7fbccc20dbd0>
<BoundClasses.BoundMatMul object at 0x7fbccc20dc30>
<BoundClasses.BoundAdd object at 0x7fbccc20dc90>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundAdd object at 0x7fbccc20dcf0>
torch.Size([1, 8, 768]) torch.Size([1, 8, 768])
<BoundClasses.BoundReduceMean object at 0x7fbccc20dd50>
<BoundClasses.BoundSub object at 0x7fbccc20ddb0>
<BoundClasses.BoundConstant object at 0x7fbccc20de10>
<BoundClasses.BoundPow object at 0x7fbccc20de70>
<BoundClasses.BoundReduceMean object at 0x7fbccc20ded0>
<BoundClasses.BoundConstant object at 0x7fbccc20df30>
<BoundClasses.BoundAdd object at 0x7fbccc20df90>
torch.Size([1, 8, 1]) torch.Size([1])
<BoundClasses.BoundSqrt object at 0x7fbccc20dff0>
<BoundClasses.BoundDiv object at 0x7fbccc20e050>
<BoundClasses.BoundMul object at 0x7fbccc20e0b0>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundAdd object at 0x7fbccc20e110>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundMatMul object at 0x7fbccc20e170>
<BoundClasses.BoundAdd object at 0x7fbccc20e1d0>
torch.Size([3072]) torch.Size([1, 8, 3072])
<BoundClasses.BoundConstant object at 0x7fbccc20e230>
<BoundClasses.BoundDiv object at 0x7fbccc20e290>
<BoundClasses.BoundErf object at 0x7fbccc20e2f0>
<BoundClasses.BoundConstant object at 0x7fbccc20e350>
<BoundClasses.BoundAdd object at 0x7fbccc20e3b0>
torch.Size([1, 8, 3072]) torch.Size([1])
<BoundClasses.BoundMul object at 0x7fbccc20e410>
torch.Size([1, 8, 3072]) torch.Size([1, 8, 3072])
<BoundClasses.BoundConstant object at 0x7fbccc20e470>
<BoundClasses.BoundMul object at 0x7fbccc20e4d0>
torch.Size([1, 8, 3072]) torch.Size([1])
<BoundClasses.BoundMatMul object at 0x7fbccc20e530>
<BoundClasses.BoundAdd object at 0x7fbccc20e590>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundAdd object at 0x7fbccc20e5f0>
torch.Size([1, 8, 768]) torch.Size([1, 8, 768])
<BoundClasses.BoundReduceMean object at 0x7fbccc20e650>
<BoundClasses.BoundSub object at 0x7fbccc20e6b0>
<BoundClasses.BoundConstant object at 0x7fbccc20e710>
<BoundClasses.BoundPow object at 0x7fbccc20e770>
<BoundClasses.BoundReduceMean object at 0x7fbccc20e7d0>
<BoundClasses.BoundConstant object at 0x7fbccc20e830>
<BoundClasses.BoundAdd object at 0x7fbccc20e890>
torch.Size([1, 8, 1]) torch.Size([1])
<BoundClasses.BoundSqrt object at 0x7fbccc20e8f0>
<BoundClasses.BoundDiv object at 0x7fbccc20e950>
<BoundClasses.BoundMul object at 0x7fbccc20e9b0>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundAdd object at 0x7fbccc20ea10>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundMatMul object at 0x7fbccc20ea70>
<BoundClasses.BoundAdd object at 0x7fbccc20ead0>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundMatMul object at 0x7fbccc20eb30>
<BoundClasses.BoundAdd object at 0x7fbccc20eb90>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundShape object at 0x7fbccc20ebf0>
<BoundClasses.BoundConstant object at 0x7fbccc20ec50>
<BoundClasses.BoundGather object at 0x7fbccc20ecb0>
<BoundClasses.BoundShape object at 0x7fbccc20ed10>
<BoundClasses.BoundConstant object at 0x7fbccc20ed70>
<BoundClasses.BoundGather object at 0x7fbccc20edd0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc20ee30>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc20ee90>
<BoundClasses.BoundConstant object at 0x7fbccc20eef0>
<BoundClasses.BoundConstant object at 0x7fbccc20ef50>
<BoundClasses.BoundConcat object at 0x7fbccc20efb0>
<BoundClasses.BoundReshape object at 0x7fbccc20f010>
<BoundClasses.BoundMatMul object at 0x7fbccc20f070>
<BoundClasses.BoundAdd object at 0x7fbccc20f0d0>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundShape object at 0x7fbccc20f130>
<BoundClasses.BoundConstant object at 0x7fbccc20f190>
<BoundClasses.BoundGather object at 0x7fbccc20f1f0>
<BoundClasses.BoundShape object at 0x7fbccc20f250>
<BoundClasses.BoundConstant object at 0x7fbccc20f2b0>
<BoundClasses.BoundGather object at 0x7fbccc20f310>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc20f370>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc20f3d0>
<BoundClasses.BoundConstant object at 0x7fbccc20f430>
<BoundClasses.BoundConstant object at 0x7fbccc20f490>
<BoundClasses.BoundConcat object at 0x7fbccc20f4f0>
<BoundClasses.BoundReshape object at 0x7fbccc20f550>
<BoundClasses.BoundTranspose object at 0x7fbccc20f5b0>
<BoundClasses.BoundShape object at 0x7fbccc20f610>
<BoundClasses.BoundConstant object at 0x7fbccc20f670>
<BoundClasses.BoundGather object at 0x7fbccc20f6d0>
<BoundClasses.BoundShape object at 0x7fbccc20f730>
<BoundClasses.BoundConstant object at 0x7fbccc20f790>
<BoundClasses.BoundGather object at 0x7fbccc20f7f0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc20f850>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc20f8b0>
<BoundClasses.BoundConstant object at 0x7fbccc20f910>
<BoundClasses.BoundConstant object at 0x7fbccc20f970>
<BoundClasses.BoundConcat object at 0x7fbccc20f9d0>
<BoundClasses.BoundReshape object at 0x7fbccc20fa30>
<BoundClasses.BoundTranspose object at 0x7fbccc20fa90>
<BoundClasses.BoundTranspose object at 0x7fbccc20faf0>
<BoundClasses.BoundMatMul object at 0x7fbccc20fb50>
<BoundClasses.BoundConstant object at 0x7fbccc20fbb0>
<BoundClasses.BoundDiv object at 0x7fbccc20fc10>
<BoundClasses.BoundAdd object at 0x7fbccc20fc70>
torch.Size([1, 12, 8, 8]) torch.Size([1, 1, 1, 8])
<BoundClasses.BoundSoftmax object at 0x7fbccc20fcd0>
<BoundClasses.BoundMatMul object at 0x7fbccc20fd30>
<BoundClasses.BoundTranspose object at 0x7fbccc20fd90>
<BoundClasses.BoundShape object at 0x7fbccc20fdf0>
<BoundClasses.BoundConstant object at 0x7fbccc20fe50>
<BoundClasses.BoundGather object at 0x7fbccc20feb0>
<BoundClasses.BoundShape object at 0x7fbccc20ff10>
<BoundClasses.BoundConstant object at 0x7fbccc20ff70>
<BoundClasses.BoundGather object at 0x7fbccc20ffd0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc20c070>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc23c0d0>
<BoundClasses.BoundConstant object at 0x7fbccc23c130>
<BoundClasses.BoundConcat object at 0x7fbccc23c190>
<BoundClasses.BoundReshape object at 0x7fbccc23c1f0>
<BoundClasses.BoundMatMul object at 0x7fbccc23c250>
<BoundClasses.BoundAdd object at 0x7fbccc23c2b0>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundAdd object at 0x7fbccc23c310>
torch.Size([1, 8, 768]) torch.Size([1, 8, 768])
<BoundClasses.BoundReduceMean object at 0x7fbccc23c370>
<BoundClasses.BoundSub object at 0x7fbccc23c3d0>
<BoundClasses.BoundConstant object at 0x7fbccc23c430>
<BoundClasses.BoundPow object at 0x7fbccc23c490>
<BoundClasses.BoundReduceMean object at 0x7fbccc23c4f0>
<BoundClasses.BoundConstant object at 0x7fbccc23c550>
<BoundClasses.BoundAdd object at 0x7fbccc23c5b0>
torch.Size([1, 8, 1]) torch.Size([1])
<BoundClasses.BoundSqrt object at 0x7fbccc23c610>
<BoundClasses.BoundDiv object at 0x7fbccc23c670>
<BoundClasses.BoundMul object at 0x7fbccc23c6d0>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundAdd object at 0x7fbccc23c730>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundMatMul object at 0x7fbccc23c790>
<BoundClasses.BoundAdd object at 0x7fbccc23c7f0>
torch.Size([3072]) torch.Size([1, 8, 3072])
<BoundClasses.BoundConstant object at 0x7fbccc23c850>
<BoundClasses.BoundDiv object at 0x7fbccc23c8b0>
<BoundClasses.BoundErf object at 0x7fbccc23c910>
<BoundClasses.BoundConstant object at 0x7fbccc23c970>
<BoundClasses.BoundAdd object at 0x7fbccc23c9d0>
torch.Size([1, 8, 3072]) torch.Size([1])
<BoundClasses.BoundMul object at 0x7fbccc23ca30>
torch.Size([1, 8, 3072]) torch.Size([1, 8, 3072])
<BoundClasses.BoundConstant object at 0x7fbccc23ca90>
<BoundClasses.BoundMul object at 0x7fbccc23caf0>
torch.Size([1, 8, 3072]) torch.Size([1])
<BoundClasses.BoundMatMul object at 0x7fbccc23cb50>
<BoundClasses.BoundAdd object at 0x7fbccc23cbb0>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundAdd object at 0x7fbccc23cc10>
torch.Size([1, 8, 768]) torch.Size([1, 8, 768])
<BoundClasses.BoundReduceMean object at 0x7fbccc23cc70>
<BoundClasses.BoundSub object at 0x7fbccc23ccd0>
<BoundClasses.BoundConstant object at 0x7fbccc23cd30>
<BoundClasses.BoundPow object at 0x7fbccc23cd90>
<BoundClasses.BoundReduceMean object at 0x7fbccc23cdf0>
<BoundClasses.BoundConstant object at 0x7fbccc23ce50>
<BoundClasses.BoundAdd object at 0x7fbccc23ceb0>
torch.Size([1, 8, 1]) torch.Size([1])
<BoundClasses.BoundSqrt object at 0x7fbccc23cf10>
<BoundClasses.BoundDiv object at 0x7fbccc23cf70>
<BoundClasses.BoundMul object at 0x7fbccc23cfd0>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundAdd object at 0x7fbccc23d030>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundMatMul object at 0x7fbccc23d090>
<BoundClasses.BoundAdd object at 0x7fbccc23d0f0>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundMatMul object at 0x7fbccc23d150>
<BoundClasses.BoundAdd object at 0x7fbccc23d1b0>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundShape object at 0x7fbccc23d210>
<BoundClasses.BoundConstant object at 0x7fbccc23d270>
<BoundClasses.BoundGather object at 0x7fbccc23d2d0>
<BoundClasses.BoundShape object at 0x7fbccc23d330>
<BoundClasses.BoundConstant object at 0x7fbccc23d390>
<BoundClasses.BoundGather object at 0x7fbccc23d3f0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc23d450>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc23d4b0>
<BoundClasses.BoundConstant object at 0x7fbccc23d510>
<BoundClasses.BoundConstant object at 0x7fbccc23d570>
<BoundClasses.BoundConcat object at 0x7fbccc23d5d0>
<BoundClasses.BoundReshape object at 0x7fbccc23d630>
<BoundClasses.BoundMatMul object at 0x7fbccc23d690>
<BoundClasses.BoundAdd object at 0x7fbccc23d6f0>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundShape object at 0x7fbccc23d750>
<BoundClasses.BoundConstant object at 0x7fbccc23d7b0>
<BoundClasses.BoundGather object at 0x7fbccc23d810>
<BoundClasses.BoundShape object at 0x7fbccc23d870>
<BoundClasses.BoundConstant object at 0x7fbccc23d8d0>
<BoundClasses.BoundGather object at 0x7fbccc23d930>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc23d990>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc23d9f0>
<BoundClasses.BoundConstant object at 0x7fbccc23da50>
<BoundClasses.BoundConstant object at 0x7fbccc23dab0>
<BoundClasses.BoundConcat object at 0x7fbccc23db10>
<BoundClasses.BoundReshape object at 0x7fbccc23db70>
<BoundClasses.BoundTranspose object at 0x7fbccc23dbd0>
<BoundClasses.BoundShape object at 0x7fbccc23dc30>
<BoundClasses.BoundConstant object at 0x7fbccc23dc90>
<BoundClasses.BoundGather object at 0x7fbccc23dcf0>
<BoundClasses.BoundShape object at 0x7fbccc23dd50>
<BoundClasses.BoundConstant object at 0x7fbccc23ddb0>
<BoundClasses.BoundGather object at 0x7fbccc23de10>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc23de70>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc23ded0>
<BoundClasses.BoundConstant object at 0x7fbccc23df30>
<BoundClasses.BoundConstant object at 0x7fbccc23df90>
<BoundClasses.BoundConcat object at 0x7fbccc23dff0>
<BoundClasses.BoundReshape object at 0x7fbccc23e050>
<BoundClasses.BoundTranspose object at 0x7fbccc23e0b0>
<BoundClasses.BoundTranspose object at 0x7fbccc23e110>
<BoundClasses.BoundMatMul object at 0x7fbccc23e170>
<BoundClasses.BoundConstant object at 0x7fbccc23e1d0>
<BoundClasses.BoundDiv object at 0x7fbccc23e230>
<BoundClasses.BoundAdd object at 0x7fbccc23e290>
torch.Size([1, 12, 8, 8]) torch.Size([1, 1, 1, 8])
<BoundClasses.BoundSoftmax object at 0x7fbccc23e2f0>
<BoundClasses.BoundMatMul object at 0x7fbccc23e350>
<BoundClasses.BoundTranspose object at 0x7fbccc23e3b0>
<BoundClasses.BoundShape object at 0x7fbccc23e410>
<BoundClasses.BoundConstant object at 0x7fbccc23e470>
<BoundClasses.BoundGather object at 0x7fbccc23e4d0>
<BoundClasses.BoundShape object at 0x7fbccc23e530>
<BoundClasses.BoundConstant object at 0x7fbccc23e590>
<BoundClasses.BoundGather object at 0x7fbccc23e5f0>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc23e650>
<BoundClasses.BoundUnsqueeze object at 0x7fbccc23e6b0>
<BoundClasses.BoundConstant object at 0x7fbccc23e710>
<BoundClasses.BoundConcat object at 0x7fbccc23e770>
<BoundClasses.BoundReshape object at 0x7fbccc23e7d0>
<BoundClasses.BoundMatMul object at 0x7fbccc23e830>
<BoundClasses.BoundAdd object at 0x7fbccc23e890>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundAdd object at 0x7fbccc23e8f0>
torch.Size([1, 8, 768]) torch.Size([1, 8, 768])
<BoundClasses.BoundReduceMean object at 0x7fbccc23e950>
<BoundClasses.BoundSub object at 0x7fbccc23e9b0>
<BoundClasses.BoundConstant object at 0x7fbccc23ea10>
<BoundClasses.BoundPow object at 0x7fbccc23ea70>
<BoundClasses.BoundReduceMean object at 0x7fbccc23ead0>
<BoundClasses.BoundConstant object at 0x7fbccc23eb30>
<BoundClasses.BoundAdd object at 0x7fbccc23eb90>
torch.Size([1, 8, 1]) torch.Size([1])
<BoundClasses.BoundSqrt object at 0x7fbccc23ebf0>
<BoundClasses.BoundDiv object at 0x7fbccc23ec50>
<BoundClasses.BoundMul object at 0x7fbccc23ecb0>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundAdd object at 0x7fbccc23ed10>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundMatMul object at 0x7fbccc23ed70>
<BoundClasses.BoundAdd object at 0x7fbccc23edd0>
torch.Size([3072]) torch.Size([1, 8, 3072])
<BoundClasses.BoundConstant object at 0x7fbccc23ee30>
<BoundClasses.BoundDiv object at 0x7fbccc23ee90>
<BoundClasses.BoundErf object at 0x7fbccc23eef0>
<BoundClasses.BoundConstant object at 0x7fbccc23ef50>
<BoundClasses.BoundAdd object at 0x7fbccc23efb0>
torch.Size([1, 8, 3072]) torch.Size([1])
<BoundClasses.BoundMul object at 0x7fbccc23f010>
torch.Size([1, 8, 3072]) torch.Size([1, 8, 3072])
<BoundClasses.BoundConstant object at 0x7fbccc23f070>
<BoundClasses.BoundMul object at 0x7fbccc23f0d0>
torch.Size([1, 8, 3072]) torch.Size([1])
<BoundClasses.BoundMatMul object at 0x7fbccc23f130>
<BoundClasses.BoundAdd object at 0x7fbccc23f190>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundAdd object at 0x7fbccc23f1f0>
torch.Size([1, 8, 768]) torch.Size([1, 8, 768])
<BoundClasses.BoundReduceMean object at 0x7fbccc23f250>
<BoundClasses.BoundSub object at 0x7fbccc23f2b0>
<BoundClasses.BoundConstant object at 0x7fbccc23f310>
<BoundClasses.BoundPow object at 0x7fbccc23f370>
<BoundClasses.BoundReduceMean object at 0x7fbccc23f3d0>
<BoundClasses.BoundConstant object at 0x7fbccc23f430>
<BoundClasses.BoundAdd object at 0x7fbccc23f490>
torch.Size([1, 8, 1]) torch.Size([1])
<BoundClasses.BoundSqrt object at 0x7fbccc23f4f0>
<BoundClasses.BoundDiv object at 0x7fbccc23f550>
<BoundClasses.BoundMul object at 0x7fbccc23f5b0>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundAdd object at 0x7fbccc23f610>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundMatMul object at 0x7fbccc23f670>
<BoundClasses.BoundAdd object at 0x7fbccc23f6d0>
torch.Size([768]) torch.Size([1, 8, 768])
<BoundClasses.BoundConstant object at 0x7fbccc23f730>
<BoundClasses.BoundDiv object at 0x7fbccc23f790>
<BoundClasses.BoundErf object at 0x7fbccc23f7f0>
<BoundClasses.BoundConstant object at 0x7fbccc23f850>
<BoundClasses.BoundAdd object at 0x7fbccc23f8b0>
torch.Size([1, 8, 768]) torch.Size([1])
<BoundClasses.BoundMul object at 0x7fbccc23f910>
torch.Size([1, 8, 768]) torch.Size([1, 8, 768])
<BoundClasses.BoundConstant object at 0x7fbccc23f970>
<BoundClasses.BoundMul object at 0x7fbccc23f9d0>
torch.Size([1, 8, 768]) torch.Size([1])
<BoundClasses.BoundReduceMean object at 0x7fbccc23fa30>
<BoundClasses.BoundSub object at 0x7fbccc23fa90>
<BoundClasses.BoundConstant object at 0x7fbccc23faf0>
<BoundClasses.BoundPow object at 0x7fbccc23fb50>
<BoundClasses.BoundReduceMean object at 0x7fbccc23fbb0>
<BoundClasses.BoundConstant object at 0x7fbccc23fc10>
<BoundClasses.BoundAdd object at 0x7fbccc23fc70>
torch.Size([1, 8, 1]) torch.Size([1])
<BoundClasses.BoundSqrt object at 0x7fbccc23fcd0>
<BoundClasses.BoundDiv object at 0x7fbccc23fd30>
<BoundClasses.BoundMul object at 0x7fbccc23fd90>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundAdd object at 0x7fbccc23fdf0>
torch.Size([1, 8, 768]) torch.Size([768])
<BoundClasses.BoundMatMul object at 0x7fbccc23fe50>
<BoundClasses.BoundAdd object at 0x7fbccc23feb0>
torch.Size([30522]) torch.Size([1, 8, 30522])
tensor(True, device='cuda:2')
tensor(0.0001, grad_fn=<MaxBackward1>)
