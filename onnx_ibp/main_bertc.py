# %%
import onnx
import onnxruntime as ort
from onnx import helper, numpy_helper
import torch
import numpy as np
from transformers import <PERSON>Token<PERSON>, AutoModel, BertForSequenceClassification
from BoundClasses import *
import logging 
import os

logging.getLogger("transformers.modeling_utils").setLevel(logging.ERROR)
torch.backends.cudnn.deterministic = True
torch.backends.cudnn.benchmark = False

def get_bert_constants(model_dir, seq_len):
    model = BertForSequenceClassification.from_pretrained(model_dir)
    constants = {
        "bert.embeddings.position_ids": model.bert.embeddings.position_ids,
        "bert.embeddings.word_embeddings.weight": model.bert.embeddings.word_embeddings.weight,
        "bert.embeddings.token_type_embeddings.weight": model.bert.embeddings.token_type_embeddings.weight,
        "bert.embeddings.position_embeddings.weight": model.bert.embeddings.position_embeddings.weight,
        "bert.embeddings.LayerNorm.weight": model.bert.embeddings.LayerNorm.weight,
        "bert.embeddings.LayerNorm.bias": model.bert.embeddings.LayerNorm.bias,
    }
    del model
    return constants

def convert_bert_layers_to_bound(model):
    graph = model.graph
    bound_nodes = []
    input_names = [inp.name for inp in graph.input]
    output_names = [out.name for out in graph.output]

    for node in graph.node:
        op_type = node.op_type
        assert op_type in bound_op_map, f"Operation {op_type} not supported."
        attr_dict = {attr.name: helper.get_attribute_value(attr) for attr in node.attribute}
        BoundClass = bound_op_map[op_type]
        bound_node = BoundClass(
            name=node.name,
            inputs=list(node.input),
            outputs=list(node.output),
            attr=attr_dict
        )
        bound_nodes.append(bound_node)

    return bound_nodes, input_names, output_names

def IBP(bound_nodes, intervals_by_name, device="cuda", eps=0):
    def format_tensor(t):
        return str(t) if t.numel() <= 10 else f"Shape: {tuple(t.shape)}"
    
    for key in intervals_by_name:
        intervals_by_name[key] = (
            intervals_by_name[key][0].to(device),
            intervals_by_name[key][1].to(device)
        )

    for node in bound_nodes:
        # print(node)
        assert isinstance(node, Bound)
        
        input_intervals = []
        input_details = []
        
        for input_name in node.inputs:
            if input_name in intervals_by_name:
                lower, upper = intervals_by_name[input_name]
                lower_device = lower.to(device)
                upper_device = upper.to(device)
                input_intervals.append((lower_device, upper_device))
                input_details.append(f"  - Input: {input_name}, {format_tensor(lower_device)}")
            else:
                raise ValueError(f"No interval found for input '{input_name}' of node '{node.name}'")
            
        # print(
        #     f"\nNode: {node.name}\n"
        #     f"Operator: {type(node)}\n"
        #     f"Inputs:\n" + "\n".join(input_details) + "\n"
        # )
        outputs_interval = node.interval_propagate(*input_intervals)

        if "/bert/embeddings/word_embeddings/Gather_output_0" in node.outputs:
            outputs_interval = (outputs_interval[0] - eps, outputs_interval[1] + eps)
        # print(node.outputs)
        # assert len(node.outputs) == 1
        output_name = node.outputs[0]
        lower_out, upper_out = outputs_interval        
        out_lower_str = format_tensor(lower_out)
        out_upper_str = format_tensor(upper_out)
        # print(
        #     f"Outputs:\n  - Output: {output_name}, Lower: {out_lower_str}, Upper: {out_upper_str}\n"
        # )

        intervals_by_name[output_name] = (
            outputs_interval[0].to(device), 
            outputs_interval[1].to(device)
        )

    lb, ub = intervals_by_name["logits"]
    return lb, ub
# %%

def main():
    # for handler in logging.root.handlers[:]:
    #     logging.root.removeHandler(handler)

    # logging.basicConfig(level=print)
    # logging.basicConfig(
    #     filename="interval_propagation.log",
    #     level=print,                   
    #     format="%(message)s",                 
    #     filemode="w"                           
    # )
    sess_opts = ort.SessionOptions()
    if "OMP_NUM_THREADS" in os.environ:
        sess_opts.inter_op_num_threads = int(os.environ["OMP_NUM_THREADS"])
        sess_opts.intra_op_num_threads = int(os.environ["OMP_NUM_THREADS"])

    
    # %%
    # Wrap inputs in a tuple
    model_dir = "bert-imdb-model-ibp"
    text = "DPLM-2 is primarily designed for generating single-chain protein sequences and their corresponding 3D structures. While the current implementation focuses on single-chain proteins, the underlying architecture could potentially be extended to handle longer sequences or multi-chain complexes. However, such adaptations would require significant modifications to the model to accurately capture the intricate interactions and structural dependencies inherent in multi-chain assemblies. Future research is needed to explore these extensions and validate their effectiveness."
    tokenizer = AutoTokenizer.from_pretrained(model_dir)
    inputs_np = tokenizer(
        text,
        return_tensors="np" 
    )

    input_ids = torch.from_numpy(inputs_np["input_ids"]).to(torch.long)
    attention_mask = torch.from_numpy(inputs_np["attention_mask"]).to(torch.long)

    dummy_input = (input_ids, attention_mask)
    model = BertForSequenceClassification.from_pretrained(model_dir, num_labels=2)
    model.train()

    torch.onnx.export(
        model, 
        dummy_input,  # Input tensor
        "bert_classification.onnx",  # Save file
        input_names=["input_ids", "attention_mask"],  # Input names
        output_names=["logits"],  # Output names
        dynamic_axes={
            "input_ids": {0: "batch_size", 1: "sequence_length"},
            "attention_mask": {0: "batch_size", 1: "sequence_length"},
            "logits": {0: "batch_size"}  # Output shape is dynamic based on batch size
        },
        training=torch.onnx.TrainingMode.TRAINING,
        do_constant_folding=False, 
        opset_version=14  # Use an appropriate ONNX opset
    )
    print("BERT classification model saved as 'bert_classification.onnx'")



    onnx_model_path = f"bert_classification.onnx"
    model = onnx.load(onnx_model_path)
    onnx.checker.check_model(model)
    session = ort.InferenceSession(onnx_model_path, providers=['CPUExecutionProvider'])

    # inputs_np = tokenizer(text, return_tensors="np")
    onnx_inputs = {
        "input_ids": inputs_np["input_ids"].astype(np.int64),
        "attention_mask": inputs_np["attention_mask"].astype(np.int64),
        # "token_type_ids": inputs_np["token_type_ids"].astype(np.int64),
    }
    outputs = session.run(None, onnx_inputs)

    onnx_inputs_torch = {k: torch.tensor(v) for k,v in onnx_inputs.items()}
    initializer_dict = {
        tensor.name: torch.tensor(numpy_helper.to_array(tensor))
        for tensor in model.graph.initializer
    }
    
    bert_constants = get_bert_constants(model_dir=model_dir, seq_len=inputs_np["input_ids"].shape[1])
    intervals_by_name = {k: (v, v) for k, v in {**onnx_inputs_torch, **initializer_dict, **bert_constants}.items()}

    # %%
    bound_nodes, input_names, output_names = convert_bert_layers_to_bound(model)
    lb, ub = IBP(bound_nodes, intervals_by_name, eps=0)

    model_hf = BertForSequenceClassification.from_pretrained(model_dir)
    model_hf.to('cuda')
    # inputs = tokenizer(text, return_tensors="pt").to('cuda')
    model_hf.train()
    with torch.no_grad():
        outputs_hf = model_hf(input_ids.cuda(), attention_mask.cuda())

    print(torch.all(lb == ub))
    print(torch.tensor(outputs[0]), outputs_hf.logits, lb.cpu())
    print((torch.tensor(outputs[0]).cpu() - lb.cpu()).abs())
    print((outputs_hf.logits.clone().detach().cpu() - lb.cpu()).abs())
    # %%

if __name__ == "__main__":
    main()
