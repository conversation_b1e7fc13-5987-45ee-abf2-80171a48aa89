Writing logs to example/train_log.txt.
Wrote original training args to example/training_args.json.
***** Running training *****
  Num examples = 20000
  Num epochs = 4
  Num clean epochs = 1
  Instantaneous batch size per device = 8
  Total train batch size (w. parallel, distributed & accumulation) = 8
  Gradient accumulation steps = 1
  Total optimization steps = 11500
==========================================================
Epoch 1
Running clean epoch 1/1
Train accuracy: 88.47%
Eval accuracy: 91.52%
Best score found. Saved model to example/best_model/
==========================================================
Epoch 2
Attacking model to generate new adversarial training set...
Writing logs to ./example/train_log.txt.
Wrote original training args to ./example/training_args.json.
***** Running training *****
  Num examples = 20000
  Num epochs = 4
  Num clean epochs = 1
  Instantaneous batch size per device = 8
  Total train batch size (w. parallel, distributed & accumulation) = 8
  Gradient accumulation steps = 1
  Total optimization steps = 11500
==========================================================
Epoch 1
Running clean epoch 1/1
Writing logs to ./example/train_log.txt.
Wrote original training args to ./example/training_args.json.
***** Running training *****
  Num examples = 20000
  Num epochs = 4
  Num clean epochs = 1
  Instantaneous batch size per device = 8
  Total train batch size (w. parallel, distributed & accumulation) = 8
  Gradient accumulation steps = 1
  Total optimization steps = 11500
==========================================================
Epoch 1
Running clean epoch 1/1
Writing logs to ./example/train_log.txt.
Wrote original training args to ./example/training_args.json.
***** Running training *****
  Num examples = 20000
  Num epochs = 4
  Num clean epochs = 1
  Instantaneous batch size per device = 8
  Total train batch size (w. parallel, distributed & accumulation) = 8
  Gradient accumulation steps = 1
  Total optimization steps = 11500
==========================================================
Epoch 1
Running clean epoch 1/1
Writing logs to ./example/train_log.txt.
Wrote original training args to ./example/training_args.json.
***** Running training *****
  Num examples = 20000
  Num epochs = 4
  Num clean epochs = 1
  Instantaneous batch size per device = 8
  Total train batch size (w. parallel, distributed & accumulation) = 8
  Gradient accumulation steps = 1
  Total optimization steps = 11500
==========================================================
Epoch 1
Running clean epoch 1/1
Writing logs to ./example/train_log.txt.
Wrote original training args to ./example/training_args.json.
***** Running training *****
  Num examples = 20000
  Num epochs = 4
  Num clean epochs = 1
  Instantaneous batch size per device = 8
  Total train batch size (w. parallel, distributed & accumulation) = 8
  Gradient accumulation steps = 1
  Total optimization steps = 11500
==========================================================
Epoch 1
Running clean epoch 1/1
Writing logs to ./example/train_log.txt.
Wrote original training args to ./example/training_args.json.
***** Running training *****
  Num examples = 20000
  Num epochs = 4
  Num clean epochs = 1
  Instantaneous batch size per device = 8
  Total train batch size (w. parallel, distributed & accumulation) = 8
  Gradient accumulation steps = 1
  Total optimization steps = 11500
==========================================================
Epoch 1
Running clean epoch 1/1
Writing logs to ./example/train_log.txt.
Wrote original training args to ./example/training_args.json.
***** Running training *****
  Num examples = 20000
  Num epochs = 4
  Num clean epochs = 1
  Instantaneous batch size per device = 8
  Total train batch size (w. parallel, distributed & accumulation) = 8
  Gradient accumulation steps = 1
  Total optimization steps = 11500
==========================================================
Epoch 1
Running clean epoch 1/1
Writing logs to ./example/train_log.txt.
Wrote original training args to ./example/training_args.json.
***** Running training *****
  Num examples = 20000
  Num epochs = 4
  Num clean epochs = 1
  Instantaneous batch size per device = 8
  Total train batch size (w. parallel, distributed & accumulation) = 8
  Gradient accumulation steps = 1
  Total optimization steps = 11500
==========================================================
Epoch 1
Running clean epoch 1/1
Train accuracy: 88.79%
Eval accuracy: 89.96%
Best score found. Saved model to ./example/best_model/
==========================================================
Epoch 2
Attacking model to generate new adversarial training set...
Writing logs to ./example/train_log.txt.
Wrote original training args to ./example/training_args.json.
***** Running training *****
  Num examples = 20000
  Num epochs = 4
  Num clean epochs = 1
  Instantaneous batch size per device = 8
  Total train batch size (w. parallel, distributed & accumulation) = 8
  Gradient accumulation steps = 1
  Total optimization steps = 10039
==========================================================
Epoch 1
Running clean epoch 1/1
Writing logs to ./example/train_log.txt.
Wrote original training args to ./example/training_args.json.
***** Running training *****
  Num examples = 20000
  Num epochs = 4
  Num clean epochs = 1
  Instantaneous batch size per device = 8
  Total train batch size (w. parallel, distributed & accumulation) = 8
  Gradient accumulation steps = 1
  Total optimization steps = 10003
==========================================================
Epoch 1
Running clean epoch 1/1
Writing logs to ./example/train_log.txt.
Wrote original training args to ./example/training_args.json.
***** Running training *****
  Num examples = 20000
  Num epochs = 4
  Num clean epochs = 0
  Instantaneous batch size per device = 8
  Total train batch size (w. parallel, distributed & accumulation) = 8
  Gradient accumulation steps = 1
  Total optimization steps = 10004
==========================================================
Epoch 1
Attacking model to generate new adversarial training set...
Total number of attack results: 7
Attack success rate: 100.00% [2 / 2]
Writing logs to ./example/train_log.txt.
Wrote original training args to ./example/training_args.json.
***** Running training *****
  Num examples = 20000
  Num epochs = 4
  Num clean epochs = 0
  Instantaneous batch size per device = 8
  Total train batch size (w. parallel, distributed & accumulation) = 8
  Gradient accumulation steps = 1
  Total optimization steps = 10012
==========================================================
Epoch 1
Attacking model to generate new adversarial training set...
Total number of attack results: 239
Attack success rate: 16.53% [20 / 121]
Writing logs to ./example/train_log.txt.
Wrote original training args to ./example/training_args.json.
***** Running training *****
  Num examples = 20000
  Num epochs = 4
  Num clean epochs = 0
  Instantaneous batch size per device = 8
  Total train batch size (w. parallel, distributed & accumulation) = 8
  Gradient accumulation steps = 1
  Total optimization steps = 10008
==========================================================
Epoch 1
Attacking model to generate new adversarial training set...
Total number of attack results: 172
Attack success rate: 12.50% [10 / 80]
Writing logs to ./example/train_log.txt.
Wrote original training args to ./example/training_args.json.
***** Running training *****
  Num examples = 20000
  Num epochs = 4
  Num clean epochs = 0
  Instantaneous batch size per device = 8
  Total train batch size (w. parallel, distributed & accumulation) = 8
  Gradient accumulation steps = 1
  Total optimization steps = 10004
==========================================================
Epoch 1
Attacking model to generate new adversarial training set...
Total number of attack results: 11
Attack success rate: 33.33% [2 / 6]
Writing logs to ./example/train_log.txt.
Wrote original training args to ./example/training_args.json.
***** Running training *****
  Num examples = 20000
  Num epochs = 4
  Num clean epochs = 0
  Instantaneous batch size per device = 8
  Total train batch size (w. parallel, distributed & accumulation) = 8
  Gradient accumulation steps = 1
  Total optimization steps = 10004
==========================================================
Epoch 1
Attacking model to generate new adversarial training set...
Total number of attack results: 6
Attack success rate: 100.00% [2 / 2]
Writing logs to ./example/train_log.txt.
Wrote original training args to ./example/training_args.json.
***** Running training *****
  Num examples = 20000
  Num epochs = 4
  Num clean epochs = 1
  Instantaneous batch size per device = 8
  Total train batch size (w. parallel, distributed & accumulation) = 8
  Gradient accumulation steps = 1
  Total optimization steps = 11500
==========================================================
Epoch 1
Running clean epoch 1/1
Writing logs to ./example/train_log.txt.
Wrote original training args to ./example/training_args.json.
***** Running training *****
  Num examples = 20000
  Num epochs = 4
  Num clean epochs = 0
  Instantaneous batch size per device = 8
  Total train batch size (w. parallel, distributed & accumulation) = 8
  Gradient accumulation steps = 1
  Total optimization steps = 10012
==========================================================
Epoch 1
Attacking model to generate new adversarial training set...
Total number of attack results: 58
Attack success rate: 95.24% [20 / 21]
Writing logs to ./example/train_log.txt.
Wrote original training args to ./example/training_args.json.
***** Running training *****
  Num examples = 20000
  Num epochs = 4
  Num clean epochs = 1
  Instantaneous batch size per device = 8
  Total train batch size (w. parallel, distributed & accumulation) = 8
  Gradient accumulation steps = 1
  Total optimization steps = 11500
==========================================================
Epoch 1
Running clean epoch 1/1
Train accuracy: 88.73%
Eval accuracy: 91.06%
Best score found. Saved model to ./example/best_model/
==========================================================
Epoch 2
Attacking model to generate new adversarial training set...
Total number of attack results: 8059
Attack success rate: 52.06% [4000 / 7684]
Train accuracy: 94.46%
Eval accuracy: 93.04%
Best score found. Saved model to ./example/best_model/
==========================================================
Epoch 3
Attacking model to generate new adversarial training set...
Total number of attack results: 10450
Attack success rate: 38.68% [4000 / 10341]
Train accuracy: 97.20%
Eval accuracy: 93.66%
Best score found. Saved model to ./example/best_model/
==========================================================
Epoch 4
Attacking model to generate new adversarial training set...
Total number of attack results: 12150
Attack success rate: 32.98% [4000 / 12129]
Train accuracy: 98.26%
Eval accuracy: 93.98%
Best score found. Saved model to ./example/best_model/
Wrote README to ./example/README.md.
