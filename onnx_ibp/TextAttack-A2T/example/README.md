## TextAttack Model Card

            This `bert` model was fine-tuned using TextAttack. The model was fine-tuned
            for 4 epochs with a batch size of 8,
             a maximum sequence length of 512, and an initial learning rate of 5e-05.
            Since this was a classification task, the model was trained with a cross-entropy loss function.
            The best score the model achieved on this task was 0.9398, as measured by the
            eval set accuracy, found after 4 epochs.

            For more information, check out [TextAttack on Github](https://github.com/QData/TextAttack).
