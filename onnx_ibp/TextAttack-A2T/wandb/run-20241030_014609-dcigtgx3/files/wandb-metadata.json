{"os": "Linux-5.15.0-122-generic-x86_64-with-glibc2.35", "python": "3.10.10", "heartbeatAt": "2024-10-30T01:46:09.610825", "startedAt": "2024-10-30T01:46:09.199895", "docker": null, "cuda": null, "args": ["--train", "imdb", "--eval", "imdb", "--model-type", "bert", "--model-save-path", "./example", "--num-epochs", "4", "--num-clean-epochs", "1", "--attack-epoch-interval", "1", "--attack", "a2t", "--learning-rate", "5e-5", "--num-warmup-steps", "100", "--grad-accumu-steps", "1", "--checkpoint-interval-epochs", "1", "--seed", "42", "--num-adv-examples", "0.0001"], "state": "running", "program": "/home/<USER>/auto_LiRPA/examples/language/TextAttack-A2T/train.py", "codePath": "train.py", "git": {"remote": "https://github.com/QData/TextAttack-A2T.git", "commit": "91a20925858801cabeec2b89c598bb146e019b45"}, "email": "<EMAIL>", "root": "/home/<USER>/auto_LiRPA/examples/language/TextAttack-A2T", "host": "focalcluster", "username": "enyij2", "executable": "/opt/anaconda/bin/python", "cpu_count": 24, "cpu_count_logical": 48, "cpu_freq": {"current": 2967.1219791666667, "min": 0.0, "max": 0.0}, "cpu_freq_per_core": [{"current": 2909.175, "min": 0.0, "max": 0.0}, {"current": 2999.999, "min": 0.0, "max": 0.0}, {"current": 2987.386, "min": 0.0, "max": 0.0}, {"current": 3000.0, "min": 0.0, "max": 0.0}, {"current": 2965.325, "min": 0.0, "max": 0.0}, {"current": 2903.768, "min": 0.0, "max": 0.0}, {"current": 2974.048, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 2987.387, "min": 0.0, "max": 0.0}, {"current": 3000.0, "min": 0.0, "max": 0.0}, {"current": 2987.382, "min": 0.0, "max": 0.0}, {"current": 2963.781, "min": 0.0, "max": 0.0}, {"current": 2987.388, "min": 0.0, "max": 0.0}, {"current": 2945.218, "min": 0.0, "max": 0.0}, {"current": 2951.749, "min": 0.0, "max": 0.0}, {"current": 2999.999, "min": 0.0, "max": 0.0}, {"current": 2987.384, "min": 0.0, "max": 0.0}, {"current": 3000.0, "min": 0.0, "max": 0.0}, {"current": 2987.385, "min": 0.0, "max": 0.0}, {"current": 3000.001, "min": 0.0, "max": 0.0}, {"current": 2987.383, "min": 0.0, "max": 0.0}, {"current": 3000.0, "min": 0.0, "max": 0.0}, {"current": 2987.386, "min": 0.0, "max": 0.0}, {"current": 2999.998, "min": 0.0, "max": 0.0}, {"current": 2907.824, "min": 0.0, "max": 0.0}, {"current": 2999.999, "min": 0.0, "max": 0.0}, {"current": 2987.388, "min": 0.0, "max": 0.0}, {"current": 3000.0, "min": 0.0, "max": 0.0}, {"current": 2966.296, "min": 0.0, "max": 0.0}, {"current": 2904.722, "min": 0.0, "max": 0.0}, {"current": 2987.385, "min": 0.0, "max": 0.0}, {"current": 2986.968, "min": 0.0, "max": 0.0}, {"current": 2987.386, "min": 0.0, "max": 0.0}, {"current": 3000.0, "min": 0.0, "max": 0.0}, {"current": 2984.929, "min": 0.0, "max": 0.0}, {"current": 2955.134, "min": 0.0, "max": 0.0}, {"current": 2986.285, "min": 0.0, "max": 0.0}, {"current": 2944.286, "min": 0.0, "max": 0.0}, {"current": 2961.59, "min": 0.0, "max": 0.0}, {"current": 3000.001, "min": 0.0, "max": 0.0}, {"current": 2987.385, "min": 0.0, "max": 0.0}, {"current": 3000.0, "min": 0.0, "max": 0.0}, {"current": 2987.384, "min": 0.0, "max": 0.0}, {"current": 3000.0, "min": 0.0, "max": 0.0}, {"current": 2987.369, "min": 0.0, "max": 0.0}, {"current": 2999.999, "min": 0.0, "max": 0.0}, {"current": 2987.384, "min": 0.0, "max": 0.0}, {"current": 2999.999, "min": 0.0, "max": 0.0}], "disk": {"total": 6905.851509094238, "used": 4966.938152313232}, "gpu": "NVIDIA A100-PCIE-40GB", "gpu_count": 4, "gpu_devices": [{"name": "NVIDIA A100-PCIE-40GB", "memory_total": 42949672960}, {"name": "NVIDIA A100-PCIE-40GB", "memory_total": 42949672960}, {"name": "NVIDIA A100-PCIE-40GB", "memory_total": 42949672960}, {"name": "NVIDIA A100-PCIE-40GB", "memory_total": 42949672960}], "memory": {"total": 187.37110137939453}}