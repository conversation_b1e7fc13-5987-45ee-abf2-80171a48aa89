absl-py==2.0.0
accelerate==0.34.0.dev0
aiofiles==23.1.0
aiohttp==3.9.4
aiosignal==1.3.1
alabaster==0.7.12
alembic==1.13.1
altair==5.0.1
anaconda-client==1.11.1
anaconda-navigator==2.4.0
anaconda-project==0.11.1
annotated-types==0.6.0
anthropic==0.25.6
antlr4-python3-runtime==4.9.3
anyio==3.5.0
anytree==2.12.1
appdirs==1.4.4
appnope==0.1.3
argon2-cffi-bindings==21.2.0
argon2-cffi==21.3.0
arrow==1.2.3
astroid==2.14.2
astropy==5.1
asttokens==2.2.1
astunparse==1.6.3
async-timeout==4.0.2
atomicwrites==1.4.0
attrs==22.2.0
auto-lirpa==0.3
autoattack==0.1
autocommand==2.2.2
automat==20.2.0
autopep8==1.6.0
babel==2.11.0
backcall==0.2.0
backports.functools-lru-cache==1.6.4
backports.tarfile==1.2.0
backports.tempfile==1.0
backports.weakref==1.0.post1
bcrypt==3.2.0
beautifulsoup4==4.11.1
bert-score==0.3.13
binaryornot==0.4.4
bioc==2.1
bitsandbytes==0.43.3
black==22.6.0
bleach==4.1.0
blessed==1.20.0
blis==0.7.11
bokeh==2.4.3
boltons==23.0.0
boto3==1.35.48
boto==2.49.0
botocore==1.35.48
bottleneck==1.3.5
bound-propagation==0.4.1
brotlipy==0.7.0
bunch==1.0.1
cachetools==5.3.0
call-function-with-timeout==1.1.1
captum==0.7.0
catalogue==2.0.10
certifi==2023.7.22
cffi==1.15.1
chardet==4.0.0
charset-normalizer==3.1.0
clarabel==0.9.0
click==8.0.4
cloudpathlib==0.16.0
cloudpickle==2.0.0
clyent==1.2.2
cmake==3.27.0
colorama==0.4.6
colorcet==3.0.1
coloredlogs==15.0.1
colorlog==6.8.2
comet-ml==3.39.2
comm==0.1.2
conda-build==3.23.3
conda-content-trust==0.1.3
conda-pack==0.6.0
conda-package-handling==2.0.2
conda-package-streaming==0.7.0
conda-repo-cli==1.0.27
conda-token==0.4.0
conda-verify==3.4.2
conda==23.3.1
confection==0.1.4
configobj==5.0.8
conllu==4.5.3
constantly==15.1.0
contextlib2==21.6.0
contourpy==1.0.5
cookiecutter==1.7.3
cox==0.1.post3
cryptography==39.0.1
cssselect==1.1.0
cvc5==1.0.8
cvxpy==1.5.2
cycler==0.11.0
cymem==2.0.8
cytoolz==0.12.0
daal4py==2023.0.2
dask==2022.7.0
dataproperty==1.0.1
datasets==3.0.2
datashader==0.14.4
datashape==0.5.4
datasketch==1.6.4
debugpy==1.6.6
decorator==5.1.1
defusedxml==0.7.1
deprecated==1.2.14
detectors==0.1.11
diff-match-patch==20200713
dill==0.3.6
diskcache==5.6.3
distributed==2022.7.0
distro==1.9.0
dm-sonnet==2.0.1
dm-tree==0.1.8
dnspython==2.6.1
docker-pycreds==0.4.0
docker==6.1.3
docopt==0.6.2
docstring-parser==0.16
docstring-to-markdown==0.11
docutils==0.18.1
dulwich==0.21.7
ecos==2.0.14
editdistance==0.8.1
einops==0.3.2
email-validator==2.2.0
en-core-web-sm==3.7.1
entrypoints==0.4
et-xmlfile==1.1.0
evalplus==0.1.5
evaluate==0.4.2
everett==3.1.0
executing==1.2.0
fairscale==0.4.13
faiss-cpu==1.7.4
farama-notifications==0.0.4
fastapi-cli==0.0.4
fastapi==0.111.0
fastchat==0.1.0
fastjsonschema==2.16.2
ffmpy==0.3.1
filelock==3.16.1
fire==0.5.0
flair==0.14.0
flake8==6.0.0
flask==2.2.2
flatbuffers==24.3.25
flatten-dict==0.4.2
flit-core==3.6.0
fonttools==4.25.0
frozenlist==1.3.3
fschat==0.2.36
fsspec==2023.9.2
fst-pso==1.8.1
ftfy==6.3.0
future==0.18.3
fuzzytm==2.0.5
gast==0.4.0
gdown==5.2.0
gensim==4.3.0
gitdb==4.0.10
gitpython==3.1.31
glob2==0.7
gmpy2==2.1.2
google-ai-generativelanguage==0.6.2
google-api-core==2.18.0
google-api-python-client==2.126.0
google-auth-httplib2==0.2.0
google-auth-oauthlib==0.4.6
google-auth==2.29.0
google-cloud-aiplatform==1.48.0
google-cloud-bigquery==3.21.0
google-cloud-core==2.4.1
google-cloud-resource-manager==1.12.3
google-cloud-storage==2.16.0
google-crc32c==1.5.0
google-generativeai==0.5.2
google-pasta==0.2.0
google-resumable-media==2.7.0
googleapis-common-protos==1.63.0
gpustat==1.1.1
gputil==1.4.0
gradio-client==0.3.0
gradio==3.39.0
graphviz==0.20.1
greenlet==2.0.1
grpc-google-iam-v1==0.13.0
grpcio-status==1.62.2
grpcio==1.62.2
gurobipy==10.0.3
gym-notices==0.0.8
gym==0.26.2
gymnasium==0.28.1
h11==0.14.0
h5py==3.12.1
heapdict==1.0.1
holoviews==1.15.4
httpcore==1.0.5
httplib2==0.22.0
httptools==0.6.1
httpx==0.25.2
huggingface-hub==0.24.5
humanfriendly==10.0
hvplot==0.8.2
hydra-core==1.3.2
hyperlink==21.0.0
idna==3.7
imageio==2.34.1
imagesize==1.4.1
imbalanced-learn==0.10.1
importlib-metadata==6.0.0
importlib-resources==6.4.0
incremental==21.3.0
inflect==7.3.1
inflection==0.5.1
iniconfig==1.1.1
inquirerpy==0.3.4
intake==0.6.7
interegular==0.3.3
interval-bound-propagation==1.1
intervaltree==3.1.0
ipykernel==6.21.3
ipython-genutils==0.2.0
ipython==8.11.0
ipywidgets==7.6.5
isort==5.9.3
itemadapter==0.3.0
itemloaders==1.0.4
itsdangerous==2.0.1
jaraco.context==5.3.0
jaraco.functools==4.0.1
jaraco.text==3.12.1
jax-jumpy==1.0.0
jaxtyping==0.2.31
jedi==0.18.2
jeepney==0.7.1
jellyfish==0.9.0
jieba==0.42.1
jinja2-time==0.2.0
jinja2==3.1.4
jiter==0.5.0
jmespath==0.10.0
joblib==1.1.1
json5==0.9.6
jsonlines==4.0.0
jsonpatch==1.32
jsonpointer==2.0
jsonschema==4.17.3
jupyter-client==8.0.3
jupyter-console==6.6.2
jupyter-core==5.2.0
jupyter-server==1.23.4
jupyter==1.0.0
jupyterlab-pygments==0.1.2
jupyterlab-server==2.19.0
jupyterlab-widgets==1.0.0
jupyterlab==3.5.3
keras-preprocessing==1.1.2
keras==3.6.0
keyring==23.4.0
kiwisolver==1.4.4
kornia==0.6.1
langcodes==3.3.0
langdetect==1.0.9
language-tool-python==2.8.1
lark==1.1.9
lazy-loader==0.4
lazy-object-proxy==1.6.0
lemminflect==0.2.3
libarchive-c==2.9
libclang==16.0.6
libmambapy==1.4.2
lightning-utilities==0.8.0
lime==*******
linkify-it-py==2.0.2
lit==16.0.6
littleutils==0.2.2
llm-attacks==0.0.1
llvmlite==0.39.1
lm-eval==0.4.2
lm-format-enforcer==0.10.1
locket==1.0.0
lru-dict==1.3.0
lxml==4.9.2
lz4==3.1.3
mako==1.3.3
mamba==1.4.2
maraboupy==2.0.0
markdown-it-py==2.2.0
markdown2==2.4.10
markdown==3.4.1
markupsafe==3.0.2
matplotlib-inline==0.1.6
matplotlib==3.8.4
mbstrdecoder==1.1.3
mccabe==0.7.0
mdit-py-plugins==0.3.3
mdurl==0.1.2
miniful==0.0.6
mistralai==0.1.8
mistune==0.8.4
mkl-fft==1.3.1
mkl-random==1.2.2
mkl-service==2.4.0
ml-collections==0.1.1
ml-dtypes==0.4.1
mock==4.0.3
more-itertools==10.2.0
mpld3==0.5.10
mpmath==1.3.0
msgpack==1.0.3
multidict==6.0.4
multipledispatch==0.6.0
multiprocess==0.70.14
munkres==1.1.4
murmurhash==1.0.10
mypy-extensions==0.4.3
namex==0.0.8
navigator-updater==0.3.0
nbclassic==0.5.2
nbclient==0.5.13
nbconvert==6.5.4
nbformat==5.7.0
nest-asyncio==1.5.6
networkx==3.4.2
nh3==0.2.14
ninja==********
nltk==3.7
notebook-shim==0.2.2
notebook==6.5.2
num2words==0.5.13
numba==0.56.4
numexpr==2.8.4
numpy==1.26.4
numpydoc==1.5.0
nvidia-cublas-cu11==*********
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu11==11.8.87
nvidia-cuda-cupti-cu12==12.4.127
nvidia-cuda-nvrtc-cu11==11.8.89
nvidia-cuda-nvrtc-cu12==12.4.127
nvidia-cuda-runtime-cu11==11.8.89
nvidia-cuda-runtime-cu12==12.4.127
nvidia-cudnn-cu11==********
nvidia-cudnn-cu12==********
nvidia-cufft-cu11==*********
nvidia-cufft-cu12==********
nvidia-curand-cu11==*********
nvidia-curand-cu12==**********
nvidia-cusolver-cu11==*********
nvidia-cusolver-cu12==********
nvidia-cusparse-cu11==*********
nvidia-cusparse-cu12==**********
nvidia-ml-py==11.525.112
nvidia-nccl-cu11==2.21.5
nvidia-nccl-cu12==2.21.5
nvidia-nvjitlink-cu12==12.4.127
nvidia-nvtx-cu11==11.8.86
nvidia-nvtx-cu12==12.4.127
nvitop==1.1.2
oauthlib==3.2.2
ogb==1.3.6
omegaconf==2.3.0
onnx2pytorch==0.4.1
onnx2torch==1.5.15
onnx==1.16.0
onnxoptimizer==0.3.13
onnxruntime==1.19.2
openai==1.40.6
opencv-python==********
openhownet==2.0
openpyxl==3.0.10
opt-einsum==3.3.0
optree==0.13.0
optuna==3.6.1
ordered-set==4.1.0
orjson==3.10.1
osqp==0.6.7
outdated==0.2.2
outlines==0.0.45
packaging==23.0
pandas==1.4.0
pandocfilters==1.5.0
panel==0.14.3
param==1.12.3
parse==1.19.1
parsel==1.6.0
parso==0.8.3
partd==1.2.0
pathlib==1.0.1
pathspec==0.10.3
pathtools==0.1.2
pathvalidate==3.2.0
patsy==0.5.3
peft==0.4.0
pep8==1.7.1
pexpect==4.8.0
pfzy==0.3.4
pickleshare==0.7.5
pillow==9.1.0
pinyin==0.4.0
pip==24.3.1
pkginfo==1.8.3
platformdirs==4.2.2
plotly==5.9.0
pluggy==1.0.0
ply==3.11
polytope==0.2.5
pooch==1.4.0
portalocker==2.8.2
poyo==0.5.0
pptree==3.1
preshed==3.0.9
prometheus-client==0.20.0
prometheus-fastapi-instrumentator==7.0.0
prompt-toolkit==3.0.38
protego==0.1.16
proto-plus==1.23.0
protobuf==3.20.3
psutil==5.9.4
ptyprocess==0.7.0
pure-eval==0.2.2
py-cpuinfo==9.0.0
py3nvml==0.2.7
py==1.11.0
pyairports==2.1.1
pyarrow-hotfix==0.6
pyarrow==16.1.0
pyasn1-modules==0.2.8
pyasn1==0.4.8
pybind11==2.12.0
pycodestyle==2.10.0
pycosat==0.6.4
pycountry==24.6.1
pycparser==2.21
pyct==0.5.0
pydantic-core==2.18.1
pydantic==2.7.0
pydeprecate==0.3.1
pydispatcher==2.0.5
pydocstyle==6.3.0
pydub==0.25.1
pyerfa==2.0.0
pyflakes==3.0.1
pyfume==0.2.25
pygments==2.15.0
pyhamcrest==2.0.2
pyjnius==1.6.1
pyjwt==2.4.0
pylint-venv==2.3.0
pylint==2.16.2
pyls-spyder==0.4.0
pynvml==11.5.0
pyodbc==4.0.34
pyopenssl==23.0.0
pyparsing==3.0.9
pyqt5-sip==12.11.0
pyqt5==5.15.7
pyqtwebengine==5.15.4
pyrsistent==0.18.0
pysocks==1.7.1
pytablewriter==1.2.0
pytest-order==1.1.0
pytest==7.1.2
python-box==6.1.0
python-dateutil==2.8.2
python-decouple==3.8
python-dotenv==1.0.1
python-lsp-black==1.2.1
python-lsp-jsonrpc==1.0.0
python-lsp-server==1.7.1
python-multipart==0.0.9
python-slugify==5.0.2
python-snappy==0.6.1
pytoolconfig==1.2.5
pytorch-lightning==1.4.9
pytorch-revgrad==0.2.0
pytz==2022.7
pyviz-comms==2.0.2
pywavelets==1.4.1
pyxdg==0.27
pyyaml==6.0
pyzmq==25.0.0
qdarkstyle==3.0.2
qdldl==0.1.7.post3
qstylizer==0.2.2
qtawesome==1.2.2
qtconsole==5.4.0
qtpy==2.2.0
queuelib==1.5.0
ray==2.11.0
records==0.6.0
referencing==0.34.0
regex==2022.7.9
requests-file==1.5.1
requests-oauthlib==1.3.1
requests-toolbelt==1.0.0
requests==2.32.2
responses==0.18.0
rich==13.5.2
robustness==1.2.1.post2
rope==1.7.0
rouge-score==0.1.2
rpds-py==0.18.0
rsa==4.9
rtree==1.0.1
ruamel-yaml-conda==0.17.21
ruamel.yaml.clib==0.2.6
ruamel.yaml==0.17.21
s3transfer==0.10.3
sacrebleu==2.4.2
safetensors==0.4.3
scikit-image==0.23.2
scikit-learn-intelex==20230228.214242
scikit-learn==1.2.1
scipy==1.9.1
scrapy==2.8.0
scs==3.2.4.post3
seaborn==0.12.2
secretstorage==3.3.1
segtok==1.5.11
semantic-version==2.10.0
semver==3.0.2
send2trash==1.8.0
sentence-transformers==2.7.0
sentencepiece==0.1.98
sentry-sdk==1.22.2
service-identity==18.1.0
setproctitle==1.3.2
setuptools==72.2.0
shapely==2.0.4
shellingham==1.5.4
shimmy==0.2.1
shortuuid==1.0.11
shtab==1.7.1
simpful==2.11.0
simplejson==3.19.2
sip==6.6.2
six==1.16.0
sklearn==0.0.post5
smart-open==5.2.1
smmap==5.0.0
sniffio==1.2.0
snowballstemmer==2.2.0
sortedcontainers==2.4.0
soupsieve==2.3.2.post1
spacy-legacy==3.0.12
spacy-loggers==1.0.5
spacy==3.7.2
sphinx==5.0.2
sphinxcontrib-applehelp==1.0.2
sphinxcontrib-devhelp==1.0.2
sphinxcontrib-htmlhelp==2.0.0
sphinxcontrib-jsmath==1.0.1
sphinxcontrib-qthelp==1.0.3
sphinxcontrib-serializinghtml==1.1.5
spyder-kernels==2.4.1
spyder==5.4.1
sqlalchemy==2.0.31
sqlitedict==2.1.0
srsly==2.4.8
stable-baselines3==2.3.2
stack-data==0.6.2
starlette==0.37.2
statsmodels==0.13.5
svgwrite==1.4.3
sympy==1.13.1
tabledata==1.3.3
tables==3.7.0
tablib==3.6.1
tabulate==0.8.10
tbb==0.2
tblib==1.7.0
tcolorpy==0.1.6
tempdir==0.7.1
tenacity==8.2.2
tensor-parallel==2.0.0
tensorboard-data-server==0.7.2
tensorboard-plugin-wit==1.8.1
tensorboard==2.18.0
tensorboardx==2.6
tensorflow-estimator==2.9.0
tensorflow-hub==0.16.1
tensorflow-io-gcs-filesystem==0.35.0
tensorflow-probability==0.23.0
tensorflow==2.18.0
tensorrt-cu12-bindings==10.0.1
tensorrt-cu12-libs==10.0.1
tensorrt-cu12==10.0.1
tensorrt==10.0.1
termcolor==2.2.0
terminado==0.17.1
terminaltables==3.1.10
text-unidecode==1.3
textattack==0.3.10
textdistance==4.2.1
tf-keras==2.18.0
tf2onnx==1.16.1
thinc==8.2.3
thread-with-results==1.0.1
threadpoolctl==2.2.0
three-merge==0.1.1
tifffile==2024.4.18
tiktoken==0.6.0
timm==0.8.19.dev0
tinycss2==1.2.1
tldextract==3.2.0
tokenizers==0.20.1
toml==0.10.2
tomli==2.0.1
tomlkit==0.11.1
toolz==0.12.0
torch==1.12.1+cu113
torchaudio==0.12.1+cu113
torchmetrics==0.7.0
torchopt==0.7.3
torchvision==0.13.1+cu113
tornado==6.4.1
tqdm-multiprocess==0.0.11
tqdm==4.66.6
traitlets==5.9.0
transformer-smaller-training-vocab==0.4.0
transformers==4.46.0
triton==3.1.0
trl==0.11.4
twisted==22.2.0
typeguard==2.13.3
typepy==1.3.2
typer==0.12.3
typing-extensions==4.12.2
tyro==0.8.14
tzdata==2024.2
uc-micro-py==1.0.2
ujson==5.4.0
unidecode==1.2.0
uritemplate==4.1.1
urllib3==1.26.18
uvicorn==0.23.2
uvloop==0.19.0
vllm-flash-attn==2.5.9
vllm==0.5.0.post1
w3lib==1.21.0
wandb==0.15.2
wasabi==1.1.2
watchdog==2.1.6
watchfiles==0.21.0
wavedrom==2.0.3.post3
wcwidth==0.2.6
weasel==0.3.4
webencodings==0.5.1
websocket-client==0.58.0
websockets==11.0.3
werkzeug==2.2.2
wget==3.2
whatthepatch==1.0.2
wheel==0.44.0
widgetsnbextension==3.5.2
wikipedia-api==0.7.1
wilds==2.0.0
word2number==1.1
wrapt==1.14.1
wurlitzer==3.0.2
xarray==2022.11.0
xformers==0.0.26.post1
xmltodict==0.13.0
xxhash==3.4.1
yapf==0.31.0
yarl==1.8.2
z3-solver==********
z3==0.2.0
zict==2.1.0
zipp==3.15.0
zope.interface==5.4.0
zstandard==0.19.0