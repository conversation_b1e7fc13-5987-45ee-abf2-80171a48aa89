{"os": "Linux-5.15.0-122-generic-x86_64-with-glibc2.35", "python": "3.10.10", "heartbeatAt": "2024-10-30T01:45:09.235224", "startedAt": "2024-10-30T01:45:08.911143", "docker": null, "cuda": null, "args": ["--train", "imdb", "--eval", "imdb", "--model-type", "bert", "--model-save-path", "./example", "--num-epochs", "4", "--num-clean-epochs", "1", "--attack-epoch-interval", "1", "--attack", "a2t", "--learning-rate", "5e-5", "--num-warmup-steps", "100", "--grad-accumu-steps", "1", "--checkpoint-interval-epochs", "1", "--seed", "42", "--num-adv-examples", "100"], "state": "running", "program": "/home/<USER>/auto_LiRPA/examples/language/TextAttack-A2T/train.py", "codePath": "train.py", "git": {"remote": "https://github.com/QData/TextAttack-A2T.git", "commit": "91a20925858801cabeec2b89c598bb146e019b45"}, "email": "<EMAIL>", "root": "/home/<USER>/auto_LiRPA/examples/language/TextAttack-A2T", "host": "focalcluster", "username": "enyij2", "executable": "/opt/anaconda/bin/python", "cpu_count": 24, "cpu_count_logical": 48, "cpu_freq": {"current": 2967.298625, "min": 0.0, "max": 0.0}, "cpu_freq_per_core": [{"current": 3000.0, "min": 0.0, "max": 0.0}, {"current": 3000.001, "min": 0.0, "max": 0.0}, {"current": 3000.0, "min": 0.0, "max": 0.0}, {"current": 2900.973, "min": 0.0, "max": 0.0}, {"current": 3000.0, "min": 0.0, "max": 0.0}, {"current": 2914.02, "min": 0.0, "max": 0.0}, {"current": 2999.999, "min": 0.0, "max": 0.0}, {"current": 3000.0, "min": 0.0, "max": 0.0}, {"current": 2966.263, "min": 0.0, "max": 0.0}, {"current": 2963.036, "min": 0.0, "max": 0.0}, {"current": 3000.0, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 2995.087, "min": 0.0, "max": 0.0}, {"current": 2990.425, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 3000.0, "min": 0.0, "max": 0.0}, {"current": 2999.999, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 2983.168, "min": 0.0, "max": 0.0}, {"current": 2897.144, "min": 0.0, "max": 0.0}, {"current": 3000.0, "min": 0.0, "max": 0.0}, {"current": 3000.0, "min": 0.0, "max": 0.0}, {"current": 2945.842, "min": 0.0, "max": 0.0}, {"current": 2999.999, "min": 0.0, "max": 0.0}, {"current": 2999.999, "min": 0.0, "max": 0.0}, {"current": 2999.999, "min": 0.0, "max": 0.0}, {"current": 2999.999, "min": 0.0, "max": 0.0}, {"current": 2901.017, "min": 0.0, "max": 0.0}, {"current": 2999.999, "min": 0.0, "max": 0.0}, {"current": 2915.323, "min": 0.0, "max": 0.0}, {"current": 3000.0, "min": 0.0, "max": 0.0}, {"current": 3000.0, "min": 0.0, "max": 0.0}, {"current": 2966.277, "min": 0.0, "max": 0.0}, {"current": 2958.864, "min": 0.0, "max": 0.0}, {"current": 3000.0, "min": 0.0, "max": 0.0}, {"current": 2978.656, "min": 0.0, "max": 0.0}, {"current": 2994.885, "min": 0.0, "max": 0.0}, {"current": 2990.428, "min": 0.0, "max": 0.0}, {"current": 2999.998, "min": 0.0, "max": 0.0}, {"current": 2999.999, "min": 0.0, "max": 0.0}, {"current": 3000.0, "min": 0.0, "max": 0.0}, {"current": 2971.524, "min": 0.0, "max": 0.0}, {"current": 2983.2, "min": 0.0, "max": 0.0}, {"current": 2896.477, "min": 0.0, "max": 0.0}, {"current": 3000.0, "min": 0.0, "max": 0.0}, {"current": 2999.999, "min": 0.0, "max": 0.0}, {"current": 2945.861, "min": 0.0, "max": 0.0}, {"current": 2999.999, "min": 0.0, "max": 0.0}], "disk": {"total": 6905.851509094238, "used": 4966.937690734863}, "gpu": "NVIDIA A100-PCIE-40GB", "gpu_count": 4, "gpu_devices": [{"name": "NVIDIA A100-PCIE-40GB", "memory_total": 42949672960}, {"name": "NVIDIA A100-PCIE-40GB", "memory_total": 42949672960}, {"name": "NVIDIA A100-PCIE-40GB", "memory_total": 42949672960}, {"name": "NVIDIA A100-PCIE-40GB", "memory_total": 42949672960}], "memory": {"total": 187.37110137939453}}