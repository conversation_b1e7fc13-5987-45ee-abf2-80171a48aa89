{"os": "Linux-5.15.0-122-generic-x86_64-with-glibc2.35", "python": "3.10.10", "heartbeatAt": "2024-10-30T02:30:51.300754", "startedAt": "2024-10-30T02:30:51.030632", "docker": null, "cuda": null, "args": ["--train", "imdb", "--eval", "imdb", "--model-type", "bert", "--model-save-path", "./example", "--num-epochs", "4", "--num-clean-epochs", "1", "--num-adv-examples", "0.2", "--attack-epoch-interval", "1", "--attack", "a2t", "--learning-rate", "5e-5", "--num-warmup-steps", "100", "--grad-accumu-steps", "1", "--checkpoint-interval-epochs", "1", "--seed", "42"], "state": "running", "program": "/home/<USER>/auto_LiRPA/examples/language/TextAttack-A2T/train.py", "codePath": "train.py", "git": {"remote": "https://github.com/QData/TextAttack-A2T.git", "commit": "91a20925858801cabeec2b89c598bb146e019b45"}, "email": "<EMAIL>", "root": "/home/<USER>/auto_LiRPA/examples/language/TextAttack-A2T", "host": "focalcluster", "username": "enyij2", "executable": "/opt/anaconda/bin/python", "cpu_count": 24, "cpu_count_logical": 48, "cpu_freq": {"current": 2548.0120208333333, "min": 0.0, "max": 0.0}, "cpu_freq_per_core": [{"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 3000.0, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 2990.175, "min": 0.0, "max": 0.0}, {"current": 3077.925, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 2608.863, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 2999.999, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 3000.0, "min": 0.0, "max": 0.0}, {"current": 3077.926, "min": 0.0, "max": 0.0}, {"current": 3000.0, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 3077.934, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 3077.934, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 3077.977, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 1970.617, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 2400.0, "min": 0.0, "max": 0.0}, {"current": 3010.829, "min": 0.0, "max": 0.0}, {"current": 2989.965, "min": 0.0, "max": 0.0}], "disk": {"total": 6905.851509094238, "used": 4967.003849029541}, "gpu": "NVIDIA A100-PCIE-40GB", "gpu_count": 4, "gpu_devices": [{"name": "NVIDIA A100-PCIE-40GB", "memory_total": 42949672960}, {"name": "NVIDIA A100-PCIE-40GB", "memory_total": 42949672960}, {"name": "NVIDIA A100-PCIE-40GB", "memory_total": 42949672960}, {"name": "NVIDIA A100-PCIE-40GB", "memory_total": 42949672960}], "memory": {"total": 187.37110137939453}}