wandb_version: 1

num_epochs:
  desc: null
  value: 4
num_clean_epochs:
  desc: null
  value: 1
attack_epoch_interval:
  desc: null
  value: 1
early_stopping_epochs:
  desc: null
  value: null
learning_rate:
  desc: null
  value: 5.0e-05
num_warmup_steps:
  desc: null
  value: 100
weight_decay:
  desc: null
  value: 0.01
per_device_train_batch_size:
  desc: null
  value: 8
per_device_eval_batch_size:
  desc: null
  value: 32
gradient_accumulation_steps:
  desc: null
  value: 1
random_seed:
  desc: null
  value: 42
parallel:
  desc: null
  value: false
load_best_model_at_end:
  desc: null
  value: true
alpha:
  desc: null
  value: 1.0
num_train_adv_examples:
  desc: null
  value: 0.2
query_budget_train:
  desc: null
  value: 200
attack_num_workers_per_device:
  desc: null
  value: 1
output_dir:
  desc: null
  value: ./example
checkpoint_interval_steps:
  desc: null
  value: null
checkpoint_interval_epochs:
  desc: null
  value: 1
save_last:
  desc: null
  value: true
log_to_tb:
  desc: null
  value: false
tb_log_dir:
  desc: null
  value: null
log_to_wandb:
  desc: null
  value: true
wandb_project:
  desc: null
  value: nlp-robustness
logging_interval_step:
  desc: null
  value: 10
_wandb:
  desc: null
  value:
    python_version: 3.10.10
    cli_version: 0.15.2
    framework: huggingface
    huggingface_version: 4.46.0
    is_jupyter_run: false
    is_kaggle_kernel: false
    start_time: 1730216606.133644
    t:
      1:
      - 1
      - 2
      - 3
      - 5
      - 11
      - 33
      - 41
      - 44
      - 49
      - 51
      - 53
      - 55
      - 71
      - 75
      2:
      - 1
      - 2
      - 3
      - 5
      - 11
      - 33
      - 41
      - 44
      - 49
      - 51
      - 53
      - 55
      - 71
      - 75
      3:
      - 16
      - 23
      4: 3.10.10
      5: 0.15.2
      6: 4.46.0
      8:
      - 5
