import argparse
import functools
import json
import os
import random
import math
import multiprocessing as mp

import datasets
import numpy as np
import textattack
import torch
import tqdm
import transformers
from lime.lime_text import LimeTextExplainer, IndexedString
from auto_LiRPA import BoundedModule, BoundedTensor, PerturbationLpNorm

from configs import DATASET_CONFIGS

os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'
NUM_SAMPLES_FOR_EVALUATION = 1000


def eval_ibp(args):
    if args.dataset not in DATASET_CONFIGS:
        raise ValueError()
    dataset_config = DATASET_CONFIGS[args.dataset]

    test_dataset = load_dataset(args.dataset)

    all_correct_indices = set(range(len(test_dataset)))
    # for path in args.checkpoint_paths:
    #     with open(os.path.join(path, "test_logs.json"), "r") as f:
    #         logs = json.load(f)
    #         correct_indices = logs[f"checkpoint-epoch-{args.epoch}"][args.dataset][
    #             "correct_indices"
    #         ]
    #         all_correct_indices = all_correct_indices.intersection(correct_indices)

    all_correct_indices = list(all_correct_indices)
    random.shuffle(all_correct_indices)
    indices_to_test = all_correct_indices[:NUM_SAMPLES_FOR_EVALUATION]

    test_dataset = test_dataset.select(indices_to_test)
    test_dataset = textattack.datasets.HuggingFaceDataset(
        test_dataset,
        dataset_columns=dataset_config["dataset_columns"],
        label_names=dataset_config["label_names"],
    )

    if args.model_type == "bert":
        model_type = "bert-base-uncased"
    elif args.model_type == "roberta":
        model_type = "roberta-base"
    else:
        raise ValueError(f"Unknown model type {args.model_type}.")

    print("Evaluating robustness (this might take a long time)...")

    for path in args.checkpoint_paths:
        logs = {}
        logs["indices"] = indices_to_test
        logs[f"checkpoint-epoch-{args.epoch}"] = {}
        model_path = f"{path}/checkpoint-epoch-{args.epoch}"
        model = transformers.AutoModelForSequenceClassification.from_pretrained(
            model_type
        )
        tokenizer = transformers.AutoTokenizer.from_pretrained(
            model_type, use_fast=True
        )
        model_wrapper = textattack.models.wrappers.HuggingFaceModelWrapper(
            model, tokenizer
        )
        print(f"====== {path}/checkpoint-epoch-{args.epoch} =====")
        for attack_name in args.attacks:
            log_file_name = f"{path}/{attack_name}-test-{args.epoch}"
            attack_args = textattack.AttackArgs(
                num_examples=NUM_SAMPLES_FOR_EVALUATION,
                parallel=(torch.cuda.device_count() > 1),
                disable_stdout=True,
                num_workers_per_device=1,
                query_budget=10000,
                shuffle=False,
                log_to_txt=log_file_name + ".txt",
                log_to_csv=log_file_name + ".csv",
                silent=True,
            )
            if attack_name == "a2t":
                attack = textattack.attack_recipes.A2TYoo2021.build(
                    model_wrapper, mlm=False
                )
            elif attack_name == "a2t_mlm":
                attack = textattack.attack_recipes.A2TYoo2021.build(
                    model_wrapper, mlm=True
                )
            elif attack_name == "textfooler":
                attack = textattack.attack_recipes.TextFoolerJin2019.build(
                    model_wrapper
                )
            elif attack_name == "bae":
                attack = textattack.attack_recipes.BAEGarg2019.build(model_wrapper)
            elif attack_name == "pwws":
                attack = textattack.attack_recipes.PWWSRen2019.build(model_wrapper)
            elif attack_name == "pso":
                attack = textattack.attack_recipes.PSOZang2020.build(model_wrapper)

            attacker = textattack.Attacker(attack, test_dataset, attack_args)
            for i in range(len(attacker.dataset)):
                example, ground_truth_output = attacker.dataset[i] 
                model = BoundedModule(model, example)
                # Define perturbation. Here we add Linf perturbation to input data.
                ptb = PerturbationLpNorm(norm=np.inf, eps=0.1)
                # Make the input a BoundedTensor with the pre-defined perturbation.
                example = BoundedTensor(example, ptb)
                # Regular forward propagation using BoundedTensor works as usual.
                prediction = model(example)
                # Compute LiRPA bounds using the backward mode bound propagation (CROWN).
                lb, ub = model.compute_bounds(x=(example,), method="IBP")
                # get embedding
            # results = attacker.attack_dataset()

            # (
            #     attack_success_rate,
            #     avg_num_queries,
            #     avg_pct_perturbed,
            # ) = calc_attack_stats(results)
            # logs[f"checkpoint-epoch-{args.epoch}"][attack_name] = {
            #     "attack_success_rate": attack_success_rate,
            #     "avg_num_queries": avg_num_queries,
            #     "avg_pct_perturbed": avg_pct_perturbed,
            # }

            # print(
            #     f"{attack_name}: {round(attack_success_rate, 1)} (attack success rate) | {avg_num_queries} (avg num queries) | {avg_pct_perturbed} (avg pct perturbed)"
            # )

        with open(os.path.join(path, "robustness_eval_logs.json"), "w") as f:
            json.dump(logs, f)





def eval_accuracy(args):
    print("Evaluating accuarcy")
    if args.dataset not in DATASET_CONFIGS:
        raise ValueError()
    dataset_config = DATASET_CONFIGS[args.dataset]
    test_datasets = dataset_config["eval_datasets"]
    eval_datasets = [
        (test_datasets[key], load_dataset(test_datasets[key])) for key in test_datasets
    ]

    for path in args.checkpoint_paths:
        logs = {}
        model_save_path = os.path.join(path, f"checkpoint-epoch-{args.epoch}")
        if args.model_type == "bert":
            model = transformers.BertForSequenceClassification.from_pretrained(
                "bert-base-uncased"
            )
            tokenizer = transformers.BertTokenizerFast.from_pretrained(
                "bert-base-uncased"
            )
        elif args.model_type == "roberta":
            model = transformers.RobertaForSequenceClassification.from_pretrained(
                "roberta-base"
            )
            tokenizer = transformers.RobertaTokenizerFast.from_pretrained(
                "roberta-base"
            )
        else:
            raise ValueError()

        num_gpus = torch.cuda.device_count()
        if num_gpus > 1:
            model = torch.nn.DataParallel(model)

        model.eval()
        model.cuda()

        if isinstance(model, torch.nn.DataParallel):
            eval_batch_size = 128 * num_gpus
        else:
            eval_batch_size = 128

        logs[f"checkpoint-epoch-{args.epoch}"] = {}
        print(f"====== {path}/checkpoint-epoch-{args.epoch} =====")

        for dataset_name, dataset in eval_datasets:
            input_columns = DATASET_CONFIGS[dataset_name]["dataset_columns"][0]
            collate_func = functools.partial(collate_fn, input_columns)
            dataloader = torch.utils.data.DataLoader(
                dataset, batch_size=eval_batch_size, collate_fn=collate_func
            )

            preds_list = []
            labels_list = []

            with torch.no_grad():
                for batch in dataloader:
                    input_texts, labels = batch
                    input_ids = tokenizer(
                        input_texts,
                        padding="max_length",
                        return_tensors="pt",
                        truncation=True,
                    )
                    for key in input_ids:
                        if isinstance(input_ids[key], torch.Tensor):
                            input_ids[key] = input_ids[key].cuda()
                    logits = model(**input_ids)[0]

                    preds = logits.argmax(dim=-1).detach().cpu()
                    preds_list.append(preds)
                    labels_list.append(labels)

            preds = torch.cat(preds_list)
            labels = torch.cat(labels_list)

            compare = preds == labels
            num_correct = compare.sum().item()
            accuracy = round(num_correct / len(labels), 4)
            correct = torch.nonzero(compare, as_tuple=True)[0].tolist()

            logs[f"checkpoint-epoch-{args.epoch}"][dataset_name] = {
                "accuracy": accuracy,
                "correct_indices": correct,
            }

            print(f"{dataset_name}: {accuracy}")

        if args.save_log:
            with open(
                os.path.join(
                    os.path.dirname(model_save_path), "accuracy_eval_logs.json"
                ),
                "w",
            ) as f:
                json.dump(logs, f)


def main(args):
    # for path in args.checkpoint_paths:
    #     if not os.path.exists(path):
    #         raise FileNotFoundError(f"Checkpoint path {path} not found.")
    # if args.accuracy:
    #     eval_accuracy(args)

    if args.ibp:
        eval_ibp(args)

    # if args.interpretability:
        # evaluate_interpretability(args)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--dataset",
        type=str,
        required=True,
        choices=sorted(list(DATASET_CONFIGS.keys())),
        help="Name train dataset.",
    )
    parser.add_argument(
        "--model-type",
        type=str,
        required=True,
        choices=["bert", "roberta"],
        help="Type of model. Choices: `bert` and `robert`.",
    )
    parser.add_argument(
        "--checkpoint-paths",
        type=str,
        nargs="*",
        default='None',
        help="Path of model checkpoint",
    )
    parser.add_argument(
        "--epoch", type=int, default=4, help="Epoch of model to evaluate."
    )
    parser.add_argument(
        "--save-log", action="store_true", help="Save evaluation result as log."
    )
    parser.add_argument("--accuracy", action="store_true", help="Evaluate accuracy.")
    parser.add_argument(
        "--robustness", action="store_true", help="Evaluate robustness."
    )
    attack_choices = ["a2t", "at2_mlm", "textfooler", "bae", "pwws", "pso"]
    parser.add_argument(
        "--attacks",
        type=str,
        nargs="*",
        default=None,
        help=f"Attacks to use to measure robustness. Choices are {attack_choices}.",
    )
    parser.add_argument(
        "--ibp",
        action="store_true",
        help="Evaluate ibp bounds.",
    )

    args = parser.parse_args()
    main(args)
