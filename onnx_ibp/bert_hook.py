import torch
import torch.nn as nn
import torch.optim as optim
from transformers import <PERSON><PERSON>okenizer, BertForSequenceClassification
from torch.utils.data import DataLoader
from datasets import load_dataset
from sklearn.metrics import accuracy_score
from tqdm import tqdm
from main_bertc import *

device = 'cuda:1'
model_dir = "bert-imdb-model"
text = "Some random sample text here!"
tokenizer = AutoTokenizer.from_pretrained(model_dir)
inputs = tokenizer(text, return_tensors="pt")
model = BertForSequenceClassification.from_pretrained(model_dir)
model.to(device)
model.eval()


# -----------------------------
# 1) Define a dictionary to store intermediate outputs
# -----------------------------
activations = {}

def get_activation(name):
    """
    Returns a forward hook that stores the output
    in the 'activations' dict under the given 'name'.
    """
    def hook(module, module_input, module_output):
        # Detach to avoid storing computation graph
        activations[name] = module_output[0].detach()
    return hook


# -----------------------------
# 2) Register forward hooks
#    - Embeddings
#    - Each encoder layer
# -----------------------------
# a) Hook for embeddings
model.bert.embeddings.register_forward_hook(get_activation("embeddings"))

# b) Hooks for each encoder layer (12 layers for bert-base)
for i, layer_module in enumerate(model.bert.encoder.layer):
    layer_name = f"encoder_layer_{i}"
    layer_module.register_forward_hook(get_activation(layer_name))


# -----------------------------
# 5) Forward pass
# -----------------------------
with torch.no_grad():
    inputs = inputs.to(device)
    outputs = model(**inputs)

# -----------------------------
# 6) Inspect the captured activations
# -----------------------------
print("Activations keys:", activations.keys())
# e.g. dict_keys(['embeddings', 'encoder_layer_0', 'encoder_layer_1', ..., 'encoder_layer_11'])

# Let's check shape of embeddings
# print("Embeddings shape:", activations["embeddings"].shape)
# Typically (batch_size, seq_length, hidden_size)

# Check shape of the last encoder layer
print("Last encoder layer shape:", activations["encoder_layer_11"].shape)


