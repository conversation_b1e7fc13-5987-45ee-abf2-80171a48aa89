import torch
import numpy as np
import onnx
from onnx import numpy_helper

class Bound:
    def __init__(self, name="", attr=None, inputs=[], outputs=[], **kwargs):
        self.name = name
        self.attr = attr if attr is not None else {}
        self.inputs = inputs
        self.outputs = outputs

    def interval_propagate(self, *v):
        raise NotImplementedError
    
class BoundUnsqueeze(Bound):
    def interval_propagate(self, *v):
        axes = self.attr.get("axes")
        if isinstance(axes, list):
            assert len(axes) == 1
            axes = axes[0]
        if axes:
            lower = torch.unsqueeze(v[0][0], axes)
            upper = torch.unsqueeze(v[0][1], axes)
        else:
            axes = 0
            lower = torch.unsqueeze(v[0][0], axes)
            upper = torch.unsqueeze(v[0][1], axes)
            # lower = v[0][0]
            # upper = v[0][1]
        return lower, upper

class BoundCast(Bound):
    def interval_propagate(self, *v):
        to_dtype = self.attr.get("to", torch.float)

        def is_valid_dtype_for_to(tensor, dtype):
            """Checks if dtype is valid for tensor.to(dtype)."""
            try:
                _ = tensor.to(dtype)  # Attempt conversion
                return True
            except (TypeError, RuntimeError):
                return False
            
        # if is_valid_dtype_for_to(v[0][0], to_dtype):
        #     lower = v[0][0].to(to_dtype)
        #     upper = v[0][1].to(to_dtype)
        # else:
        lower = v[0][0]
        upper = v[0][1]
        return lower, upper

class BoundConstant(Bound):
    def interval_propagate(self):
        tensor_proto = self.attr.get("value")
        device = self.attr.get("device", "cpu")
        if not isinstance(tensor_proto, onnx.TensorProto):
            raise ValueError(f"Expected an ONNX TensorProto, but got {type(tensor_proto)}")
        np_tensor = numpy_helper.to_array(tensor_proto)
        if np_tensor.ndim == 0:
            return torch.tensor([np_tensor.item()], device=device), torch.tensor([np_tensor.item()], device=device) # this could be prettier
        else:
            return torch.tensor(np_tensor[0], device=device), torch.tensor(np_tensor[0], device=device)

class BoundSub(Bound):
    def interval_propagate(self, *v):
        lower = v[0][0] - v[1][1]
        upper = v[0][1] - v[1][0]
        return lower, upper

class BoundMul(Bound):
    def interval_propagate(self, *v):
        a_l, a_u = v[0]
        b_l, b_u = v[1]
        candidates = [a_l * b_l, a_l * b_u, a_u * b_l, a_u * b_u]
        lower = torch.min(torch.min(candidates[0], candidates[1]), torch.min(candidates[2], candidates[3]))
        upper = torch.max(torch.max(candidates[0], candidates[1]), torch.max(candidates[2], candidates[3]))
        return lower, upper


class BoundShape(Bound):
    def interval_propagate(self, *v):
        device = self.attr.get("device", "cpu")
        shape = torch.tensor(v[0][0].shape, device=device)
        return shape, shape
    # def interval_propagate(self, *v):
    #     # v[0] = (x_L, x_U) => The input tensor's lower and upper bounds
    #     x_lower, x_upper = v[0]

    #     # Both lower and upper have the same shape
    #     shape_tuple = x_lower.shape  # or x_upper.shape, should be identical

    #     # Convert to a 1D PyTorch tensor of type int64
    #     shape_tensor = torch.tensor(shape_tuple, dtype=torch.int64)

    #     # For a shape node, there's no range of values—it's a fixed set of dimensions
    #     return shape_tensor, shape_tensor

class BoundGather(Bound):
    def interval_propagate(self, *v):
        device = self.attr.get("device", "cpu")
        axis = self.attr.get("axis", 0)
        indices = v[1][0].to(dtype=torch.int64, device=device)
        if axis == 0:
            lower = v[0][0][indices]
            upper = v[0][1][indices]
        else:
            # Use index_select for axes other than 0.
            # TODO: idk what this does but it runs 
            lower = torch.index_select(v[0][0], dim=axis, index=indices)
            upper = torch.index_select(v[0][1], dim=axis, index=indices)
        return lower, upper
    # def interval_propagate(self, *v):
    #     # v[0] = (x_L, x_U): Interval for data tensor X
    #     # v[1] = (i_L, i_U): Interval for indices
    #     x_L, x_U = v[0]
    #     i_L, i_U = v[1]

    #     # Axis to gather on (default: 0)
    #     axis = self.attr.get("axis", 0)

    #     # Convert indices to int64 for PyTorch gather ops
    #     # (Usually, i_L == i_U if indices are deterministic)
    #     indices = i_L.to(dtype=torch.int64)

    #     # Gather lower bounds and upper bounds
    #     gathered_lower = torch.gather(x_L, axis, indices)
    #     gathered_upper = torch.gather(x_U, axis, indices)

    #     return gathered_lower, gathered_upper

class BoundSlice(Bound):
    def interval_propagate(self, *v):
        # Try to get slicing parameters from attributes
        axes = self.attr.get("axes", None)
        steps = self.attr.get("steps", None)

        # If 'starts' is not in attr and we have extra inputs, use them
        if self.attr.get("starts") is None and len(v) >= 3:
            # Use the lower bounds of the second and third inputs as the slicing parameters.
            s_tensor = v[1][0]  # Expected to be a constant tensor, e.g., tensor([-1])
            e_tensor = v[2][0]  # Expected to be a constant tensor, e.g., tensor([9223372036854775807])
            # Convert to scalars. If the tensor is 0-dim or 1-dim, take its first element.
            if s_tensor.dim() == 0:
                starts = int(s_tensor.item())
            else:
                starts = int(s_tensor[0].item())
            if e_tensor.dim() == 0:
                ends = int(e_tensor.item())
            else:
                ends = int(e_tensor[0].item())
        else:
            starts = self.attr.get("starts")
            ends = self.attr.get("ends")

        def _slice(x):
            # If axes is None, assume x is 1D and slice it directly using the computed scalars.
            if axes is None:
                return x[starts:ends] if steps is None else x[starts:ends:steps]
            else:
                # Otherwise, create a slice object for each axis.
                slices = [slice(None)] * x.dim()
                # Here we assume starts and ends are iterable and match the length of axes.
                for ax, s, e in zip(axes, starts, ends):
                    slices[ax] = slice(s, e) if steps is None else slice(s, e, steps)
                return x[tuple(slices)]
        lower = _slice(v[0][0])
        upper = _slice(v[0][1])
        return lower, upper


class BoundAdd(Bound):
    def interval_propagate(self, *v):
        a_l, a_u = v[0]
        b_l, b_u = v[1]
        
        lower = a_l + b_l
        upper = a_u + b_u
        return lower, upper

class BoundReduceMean(Bound):
    def interval_propagate(self, *v):
        axes = self.attr.get("axes", None)
        lower = torch.mean(v[0][0], dim=axes, keepdim=True)
        upper = torch.mean(v[0][1], dim=axes, keepdim=True)
        return lower, upper

class BoundPow(Bound):
    def interval_propagate(self, *v):
        exp = v[1][0]
        assert exp == int(exp)
        exp = int(exp)
        pl, pu = torch.pow(v[0][0], exp), torch.pow(v[0][1], exp)
        if exp % 2 == 1:
            return pl, pu
        else:
            pl, pu = torch.min(pl, pu), torch.max(pl, pu)
            mask = 1 - ((v[0][0] < 0) * (v[0][1] > 0)).to(pl.dtype)
            return pl * mask, pu

class BoundSqrt(Bound):
    def interval_propagate(self, *v):
        lower = torch.sqrt(v[0][0])
        upper = torch.sqrt(v[0][1])
        return lower, upper

class BoundDiv(Bound):
    def interval_propagate(self, *v):
        a_l, a_u = v[0]
        b_l, b_u = v[1]
        candidates = [a_l / b_l, a_l / b_u, a_u / b_l, a_u / b_u]
        lower = torch.min(torch.min(candidates[0], candidates[1]), torch.min(candidates[2], candidates[3]))
        upper = torch.max(torch.max(candidates[0], candidates[1]), torch.max(candidates[2], candidates[3]))
        return lower, upper

class BoundMatMul(Bound):
    def interval_propagate(self, *v):
        x_L, x_U = v[0]
        w_L, w_U = v[1]
        mid = (x_L + x_U) / 2
        diff = (x_U - x_L) / 2
        w_abs = (w_L + w_U).abs() / 2
        center = mid.matmul(w_L)  # Note: adjust transposes if required
        deviation = diff.matmul(w_abs)
        lower = center - deviation
        upper = center + deviation
        return lower, upper

class BoundConcat(Bound):
    def interval_propagate(self, *v):
        axis = self.attr.get("axis", 0)

        lowers, uppers = [], []
        max_dim = max(tensor[0].dim() for tensor in v)

        for lower, upper in v:
            while lower.dim() < max_dim:
                lower = lower.unsqueeze(0)
                upper = upper.unsqueeze(0)

            lowers.append(lower)
            uppers.append(upper)

        # Concatenate along the specified axis
        lower = torch.cat(lowers, dim=axis)
        upper = torch.cat(uppers, dim=axis)
        return lower, upper

class BoundReshape(Bound):
    def interval_propagate(self, *v):
        input_tensor = v[0]
        shape_tensor = v[1][0]
        # print(input_tensor[0], input_tensor[1], shape_tensor)
        if shape_tensor.dim() > 1:
            shape_tensor = shape_tensor.view(-1)
        
        # if shape_tensor.ndim > 0:
        if shape_tensor.ndim == 0:
            lower = input_tensor[0].view(shape_tensor)
            upper = input_tensor[1].view(shape_tensor)
        else:
            new_shape = tuple(shape_tensor.cpu().detach().numpy().astype(int))
            lower = input_tensor[0].reshape(new_shape)
            upper = input_tensor[1].reshape(new_shape)
        return lower, upper


class BoundTranspose(Bound):
    def interval_propagate(self, *v):
        perm = self.attr.get("perm", None)
        lower = torch.transpose(v[0][0], 0, 1) if perm is None else v[0][0].permute(perm)
        upper = torch.transpose(v[0][1], 0, 1) if perm is None else v[0][1].permute(perm)
        return lower, upper

class BoundSoftmax(Bound):
    def interval_propagate(self, *v):
        axes = self.attr.get("axes", -1)
        h_L, h_U = v[0]
        shift = h_U.max(dim=axes, keepdim=True).values
        exp_L = torch.exp(h_L - shift)
        exp_U = torch.exp(h_U - shift)
        epsilon = 1e-12
        lower = exp_L / (torch.sum(exp_U, dim=axes, keepdim=True) - exp_U + exp_L + epsilon)
        upper = exp_U / (torch.sum(exp_L, dim=axes, keepdim=True) - exp_L + exp_U + epsilon)
        return lower, upper

class BoundErf(Bound):
    def interval_propagate(self, *v):
        lower = torch.erf(v[0][0])
        upper = torch.erf(v[0][1])
        return lower, upper
    
class BoundEqual(Bound):
    def interval_propagate(self, *v):
        a_l, a_u = v[0]  # Bounds for A
        b_l, b_u = v[1]  # Bounds for B

        # Check if A and B are definitely equal
        definite_equal = (a_l == a_u) & (b_l == b_u) & (a_l == b_l)

        # Check if A and B are definitely NOT equal (no overlap)
        definite_not_equal = (a_u < b_l) | (b_u < a_l)

        # If definitely equal -> output [1,1], if definitely not equal -> output [0,0], otherwise [0,1]
        lower = definite_equal.to(torch.float32)
        upper = 1 - definite_not_equal.to(torch.float32)

        return lower, upper
    
class BoundWhere(Bound):
    def interval_propagate(self, *v):
        condition = v[0][0].to(torch.bool)
        # print(condition, v[1][0], v[1][1])
        return tuple([torch.where(condition, v[1][j].long(), v[2][j].long()) for j in range(2)])
        # cond_l, cond_u = v[0]  # condition: (lower_bound, upper_bound)
        # x_l, x_u = v[1]        # X: (lower_bound, upper_bound)
        # y_l, y_u = v[2]        # Y: (lower_bound, upper_bound)

        # # Definitely True if cond_l >= 1
        # definitely_true = (cond_l >= 1)
        # # Definitely False if cond_u <= 0
        # definitely_false = (cond_u <= 0)

        # # For the lower bound
        # #  - If definitely_true => x_l
        # #  - Else if definitely_false => y_l
        # #  - Else uncertain => min(x_l, y_l)
        # lower = torch.where(
        #     definitely_true, 
        #     x_l, 
        #     torch.where(definitely_false, y_l, torch.min(x_l, y_l))
        # )

        # # For the upper bound
        # #  - If definitely_true => x_u
        # #  - Else if definitely_false => y_u
        # #  - Else uncertain => max(x_u, y_u)
        # upper = torch.where(
        #     definitely_true, 
        #     x_u, 
        #     torch.where(definitely_false, y_u, torch.max(x_u, y_u))
        # )

        # return lower, upper
    
class BoundConstantOfShape(Bound):
    def interval_propagate(self, *v):
        shape_tensor = v[0][0]  # Shape of the output tensor
        if shape_tensor.dim() > 1:
            shape_tensor = shape_tensor.view(-1)  # Flatten if necessary
        
        shape = tuple(shape_tensor.cpu().numpy().astype(int))  # Convert to tuple
        
        # Get the fill value from attributes (default is 0)
        value = self.attr.get("value", torch.tensor(0.0))

        if not isinstance(value, onnx.TensorProto):
            raise ValueError(f"Expected an ONNX TensorProto, but got {type(value)}")
        value = numpy_helper.to_array(value)
        
        # Ensure tensor type compatibility
        if not isinstance(value, torch.Tensor):
            value = torch.tensor(value, dtype=torch.float32)

        # The lower and upper bounds are the same
        lower = value.expand(shape)
        upper = value.expand(shape)
        
        device = self.attr.get("device", "cpu")
        return lower.to(device), upper.to(device)
    
class BoundExpand(Bound):
    def interval_propagate(self, *v):
        input_l, input_u = v[0]  # Extract input lower and upper bounds
        shape_tensor = v[1][0]   # Extract shape tensor

        # print(input_l, shape_tensor)

        # Ensure shape is a 1D tensor and convert to tuple
        if shape_tensor.dim() > 1:
            shape_tensor = shape_tensor.view(-1)
        requested_shape = tuple(shape_tensor.cpu().numpy().astype(int))  # Convert to tuple

        # Expand the input tensor
        # print(input_l, requested_shape)
        lower = input_l.expand(requested_shape)
        upper = input_u.expand(requested_shape)

        return lower, upper
    
class BoundGemm(BoundMatMul):
    """Interval propagation for General Matrix Multiplication (GEMM)"""
    def interval_propagate(self, *v):
        A_l, A_u = v[0]  # Lower & Upper bounds for A
        B_l, B_u = v[1]  # Lower & Upper bounds for B
        C = v[2][0] if len(v) > 2 else None  # Bias (Optional)

        # Get alpha and beta from attributes (default: alpha=1, beta=1)
        alpha = self.attr.get("alpha", 1.0)
        beta = self.attr.get("beta", 1.0)
        transA = self.attr.get("transA", 0)
        transB = self.attr.get("transB", 0)

        # Apply transposition if necessary
        if transA:
            A_l, A_u = A_l.T, A_u.T
        if transB:
            B_l, B_u = B_l.T, B_u.T

        lower, upper = super().interval_propagate((alpha * A_l, alpha * A_u), (B_l, B_u))

        # Add bias if present
        if C is not None:
            lower += beta * C
            upper += beta * C

        return lower, upper

class BoundTanh(Bound):
    def interval_propagate(self, *v):
        lower = torch.tanh(v[0][0])  # Apply tanh to lower bound
        upper = torch.tanh(v[0][1])  # Apply tanh to upper bound
        return lower, upper
    
class BoundDropout(Bound):
    def interval_propagate(self, *v):
        h_L, h_U = v[0]
        ratio = self.attr.get("ratio", None)
        # print(ratio)
        if ratio == None:
            ratio = v[1][0]
                   
        mask = torch.rand(h_L.shape, device=h_L.device) > ratio

        lower = h_L * mask / (1 - ratio)
        upper = h_U * mask / (1 - ratio)
        # print(lower, upper)
        return lower, upper


bound_op_map = {
    "Unsqueeze": BoundUnsqueeze,
    "Cast": BoundCast,
    "Constant": BoundConstant,
    "Sub": BoundSub,
    "Mul": BoundMul,
    "Shape": BoundShape,
    "Gather": BoundGather,
    "Slice": BoundSlice,
    "Add": BoundAdd,
    "ReduceMean": BoundReduceMean,
    "Pow": BoundPow,
    "Sqrt": BoundSqrt,
    "Div": BoundDiv,
    "MatMul": BoundMatMul,
    "Concat": BoundConcat,
    "Reshape": BoundReshape,
    "Transpose": BoundTranspose,
    "Softmax": BoundSoftmax,
    "Erf": BoundErf,
    "Equal": BoundEqual,
    "Where": BoundWhere,
    "ConstantOfShape": BoundConstantOfShape,
    "Expand": BoundExpand,
    "Gemm": BoundGemm,
    "Tanh": BoundTanh,
    "Dropout": BoundDropout,
}
