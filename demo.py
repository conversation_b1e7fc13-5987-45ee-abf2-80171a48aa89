import torch
from transformers import <PERSON><PERSON><PERSON><PERSON>, BertModel

def main():
    # 1. Load pre-trained BERT model and tokenizer
    model_name = "bert-base-uncased"
    tokenizer = BertTokenizer.from_pretrained(model_name)
    model = BertModel.from_pretrained(model_name)
    model.eval()

    # 2. Define input text and tokenize
    text = "Hugging Face provides state-of-the-art NLP models."
    inputs = tokenizer(text, return_tensors="pt")

    # 3. Explicitly create position_ids to avoid dynamic generation
    seq_length = inputs["input_ids"].size(1)
    inputs["position_ids"] = torch.arange(seq_length, dtype=torch.long).expand(1, -1)

    # 4. Run original forward pass
    with torch.no_grad():
        original_outputs = model(**inputs)

    # 5. Trace with torch.jit.trace (better for dynamic code)
    try:
        traced_model = torch.jit.trace(model, example_inputs=tuple(inputs.values()))
    except Exception as e:
        print(f"Tracing failed: {e}")
        return

    # 6. Run traced model
    with torch.no_grad():
        traced_outputs = traced_model(*inputs.values())

    # 7. Compare outputs
    last_hidden_diff = torch.max(
        torch.abs(original_outputs.last_hidden_state - traced_outputs.last_hidden_state)
    ).item()
    pooler_diff = torch.max(
        torch.abs(original_outputs.pooler_output - traced_outputs.pooler_output)
    ).item() if original_outputs.pooler_output is not None else 0

    print("Last hidden state max difference:", last_hidden_diff)
    print("Pooler output max difference:", pooler_diff)

if __name__ == "__main__":
    main()