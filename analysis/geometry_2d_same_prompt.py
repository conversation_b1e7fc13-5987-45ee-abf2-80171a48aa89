import random
import torch
import numpy as np
import matplotlib.pyplot as plt
from transformers import (
    AutoModelForSequenceClassification,
    AutoTokenizer,
)
from datasets import load_dataset
import textattack
from textattack.attack_results import SuccessfulAttackResult
from sklearn.manifold import TSNE
import argparse

parser = argparse.ArgumentParser()
# parser.add_argument('--attack_type', type=str, default='pruthi')
parser.add_argument('--end_dp', type=int, default=100)
args = parser.parse_args()

RANDOM_SEED = 0
# MAX_NUM_WORD_SWAPS = 3
# EPSILON = 1e-1
START_DATAPOINT = 0
END_DATAPOINT = args.end_dp
ITER_STEP = 1
NUM_EXAMPLES=10
# MODEL_NAME = "fabriceyhc/bert-base-uncased-imdb"
MODEL_NAME = "textattack/bert-base-uncased-imdb"

random.seed(RANDOM_SEED)
torch.manual_seed(RANDOM_SEED)

model = AutoModelForSequenceClassification.from_pretrained(MODEL_NAME)
model = model.to("cuda")
model.eval()
tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)

model_wrapper = textattack.models.wrappers.HuggingFaceModelWrapper(
    model=model,
    tokenizer=tokenizer,
)

dataset = load_dataset("stanfordnlp/imdb")

attack_pruthi = textattack.attack_recipes.Pruthi2019.build(
    model_wrapper=model_wrapper,
    # max_num_word_swaps=MAX_NUM_WORD_SWAPS,
)
attack_pwws = textattack.attack_recipes.PWWSRen2019.build(
    model_wrapper=model_wrapper,
)
attack_textfooler = textattack.attack_recipes.TextFoolerJin2019.build(
    model_wrapper=model_wrapper,
)

for j in range(START_DATAPOINT, END_DATAPOINT, ITER_STEP):
    datapoint = dataset["test"][j]
    prompt = datapoint["text"]
    label = datapoint["label"]

    print("Prompt:", prompt)
    print("Label:", label)

    inputs = tokenizer(
        prompt,
        padding=True,
        return_tensors="pt",
    ).to("cuda")

    if len(inputs["input_ids"][0]) > tokenizer.model_max_length:
        print("Input exceeds max length. Skipping...\n")

        continue

    with torch.no_grad():
        output = model(**inputs, output_hidden_states=True)
        original_prediction = torch.argmax(output.logits, dim=1)
        clean_hidden_states = output.hidden_states

    print("Original prediction:", original_prediction.item())

    if original_prediction != label:
        print("Original prediction does not match label. Skipping...\n")

        continue

    reps_clean = []
    reps_attack_pwws = []
    reps_attack_textfooler = []

    for i in range(NUM_EXAMPLES):
        # Set random seeds for reproducibility
        random.seed(RANDOM_SEED + i)
        torch.manual_seed(RANDOM_SEED + i)
        np.random.seed(RANDOM_SEED + i)

        attack_result_pwws = attack_pwws.attack(
            example=prompt,
            ground_truth_output=label,
        )

        # if isinstance(attack_result, SuccessfulAttackResult):
        attacked_prompt = attack_result_pwws.perturbed_text()

        # print("Attacked prompt:", attacked_prompt)

        attacked_inputs = tokenizer(
            attacked_prompt,
            padding=True,
            return_tensors="pt",
        ).to("cuda")

        attacked_output = model(
            **attacked_inputs,
            output_hidden_states=True,
        )

        attacked_hidden_states_pwws = attacked_output.hidden_states
        attacked_prediction_pwws = torch.argmax(attacked_output.logits, dim=1)

        print("PWWS Attacked prediction:", attacked_prediction_pwws.item())

        attack_result_textfooler = attack_textfooler.attack(
            example=prompt,
            ground_truth_output=label,
        )

        # if isinstance(attack_result, SuccessfulAttackResult):
        attacked_prompt = attack_result_textfooler.perturbed_text()

        # print("Attacked prompt:", attacked_prompt)

        attacked_inputs = tokenizer(
            attacked_prompt,
            padding=True,
            return_tensors="pt",
        ).to("cuda")

        attacked_output = model(
            **attacked_inputs,
            output_hidden_states=True,
        )

        attacked_hidden_states_textfooler = attacked_output.hidden_states
        attacked_prediction_textfooler = torch.argmax(attacked_output.logits, dim=1)

        print("TEXTFOOLER Attacked prediction:", attacked_prediction_textfooler.item())


        if isinstance(attack_result_pwws, SuccessfulAttackResult) and \
            isinstance(attack_result_textfooler, SuccessfulAttackResult):
            wrong_prediction = torch.nonzero(
                    attacked_prediction_textfooler != label
                ).flatten()
            
            rep_clean = []
            rep_attack_pwws = []
            rep_attack_textfooler = []

            with torch.no_grad():

                for (attacked_state_pwws, attacked_state_textfooler, clean_state) in zip(
                    attacked_hidden_states_pwws,
                    attacked_hidden_states_textfooler,
                    clean_hidden_states,
                ):
                    attacked_cls_state_pwws = attacked_state_pwws[:, 0, :]
                    attacked_cls_state_textfooler = attacked_state_textfooler[:, 0, :]
                    clean_cls_state = clean_state[wrong_prediction, 0, :]

                    rep_clean.append(np.squeeze(clean_cls_state.cpu().numpy()))
                    rep_attack_pwws.append(np.squeeze(attacked_cls_state_pwws.cpu().numpy()))
                    rep_attack_textfooler.append(np.squeeze(attacked_cls_state_textfooler.cpu().numpy()))
            
            reps_clean.append(rep_clean)
            reps_attack_pwws.append(rep_attack_pwws)
            reps_attack_textfooler.append(rep_attack_textfooler)
        
    reps_clean = np.array(reps_clean)
    reps_attack_pwws = np.array(reps_attack_pwws)
    reps_attack_textfooler = np.array(reps_attack_textfooler)
    print(reps_clean.shape, reps_attack_pwws.shape, reps_attack_textfooler.shape)

    # transform high-dim rep into 2d space
    tsne = TSNE(n_components=2, perplexity=reps_clean.shape[0]//3 + 1, random_state=42)

    # Process PWWS attack results
    for i in range(reps_clean.shape[1]):
        pos_clean, pos_attack_pwws, pos_attack_textfooler = reps_clean[:, i, :], reps_attack_pwws[:, i, :], rep_attack_textfooler[:, i, :]
        pos_clean = tsne.fit_transform(pos_clean) # [len_sample, 2]
        pos_attack_pwws = tsne.fit_transform(pos_attack_pwws) # [len_sample, 2]
        pos_attack_textfooler = tsne.fit_transform(pos_attack_textfooler) # [len_sample, 2]

        # Plot them with different colors and markers
        plt.scatter(pos_clean[:, 0], pos_clean[:, 1], color='blue', label='Clean Rep', alpha=0.6)
        plt.scatter(pos_attack_pwws[:, 0], pos_attack_pwws[:, 1], color='red', label='PWWS Attack Rep', alpha=0.6)
        plt.scatter(pos_attack_textfooler[:, 0], pos_attack_textfooler[:, 1], color='green', label='TextFooler Attack Rep', alpha=0.6)

        # Add labels and legend
        plt.title(f"Scatter plot of clean and PWWS/TextFooler attack representations: layer {i}")
        plt.xlabel("X axis")
        plt.ylabel("Y axis")
        plt.legend()
        plt.grid(True)
        plt.savefig(f"rep_same_input/geometry_2d_layer_{i}_datapoint_{j}.png")
        plt.clf()
    break