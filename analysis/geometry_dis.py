import random
import torch
import numpy as np
import matplotlib.pyplot as plt
from transformers import (
    AutoModelForSequenceClassification,
    AutoTokenizer,
)
from datasets import load_dataset
import textattack
from textattack.attack_results import SuccessfulAttackResult
import argparse

parser = argparse.ArgumentParser()
parser.add_argument('--attack_type', type=str, default='pruthi')
args = parser.parse_args()

RANDOM_SEED = 0
# MAX_NUM_WORD_SWAPS = 3
# EPSILON = 1e-1
START_DATAPOINT = 0
END_DATAPOINT = 100
ITER_STEP = 1
# MODEL_NAME = "fabriceyhc/bert-base-uncased-imdb"
MODEL_NAME = "textattack/bert-base-uncased-imdb"

random.seed(RANDOM_SEED)
torch.manual_seed(RANDOM_SEED)

model = AutoModelForSequenceClassification.from_pretrained(MODEL_NAME)
model = model.to("cuda")
model.eval()
tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)

# embedding_layer = model.get_input_embeddings()
# avg_embedding_norm = torch.linalg.vector_norm(
#     x=embedding_layer.weight,
#     ord=2,
#     dim=1,
# ).mean()
# epsilon = EPSILON * avg_embedding_norm

# print("Average embedding norm:", avg_embedding_norm.item())
# print("Epsilon:", epsilon.item())

model_wrapper = textattack.models.wrappers.HuggingFaceModelWrapper(
    model=model,
    tokenizer=tokenizer,
)

dataset = load_dataset("stanfordnlp/imdb")

# TODO: change into other attack types
attack_type = args.attack_type
if attack_type == 'pruthi':
    attack = textattack.attack_recipes.Pruthi2019.build(
        model_wrapper=model_wrapper,
        # max_num_word_swaps=MAX_NUM_WORD_SWAPS,
    )
elif attack_type == 'pwws':
    attack = textattack.attack_recipes.PWWSRen2019.build(
        model_wrapper=model_wrapper,
    )
elif attack_type == 'textfooler':
    attack = textattack.attack_recipes.TextFoolerJin2019.build(
        model_wrapper=model_wrapper,
    )
else:
    print("not supported attack type!")

diffs = []

for i in range(START_DATAPOINT, END_DATAPOINT, ITER_STEP):
    datapoint = dataset["test"][i]
    prompt = datapoint["text"]
    label = datapoint["label"]

    print("Prompt:", prompt)
    print("Label:", label)

    inputs = tokenizer(
        prompt,
        padding=True,
        return_tensors="pt",
    ).to("cuda")

    if len(inputs["input_ids"][0]) > tokenizer.model_max_length:
        print("Input exceeds max length. Skipping...\n")

        continue

    with torch.no_grad():
        output = model(**inputs, output_hidden_states=True)
        original_prediction = torch.argmax(output.logits, dim=1)
        clean_hidden_states = output.hidden_states

    print("Original prediction:", original_prediction.item())

    if original_prediction != label:
        print("Original prediction does not match label. Skipping...\n")

        continue

    attack_result = attack.attack(
        example=prompt,
        ground_truth_output=label,
    )

    if isinstance(attack_result, SuccessfulAttackResult):
        attacked_prompt = attack_result.perturbed_text()

        print("Attacked prompt:", attacked_prompt)

        attacked_inputs = tokenizer(
            attacked_prompt,
            padding=True,
            return_tensors="pt",
        ).to("cuda")

        attacked_output = model(
            **attacked_inputs,
            output_hidden_states=True,
        )

        attacked_hidden_states = attacked_output.hidden_states
        attacked_prediction = torch.argmax(attacked_output.logits, dim=1)

        print("Attacked prediction:", attacked_prediction.item())

        wrong_prediction = torch.nonzero(
                attacked_prediction != label
            ).flatten()

        with torch.no_grad():

            all_difference_norms = []

            for (attacked_state, clean_state) in zip(
                attacked_hidden_states,
                clean_hidden_states,
            ):
                attacked_cls_state = attacked_state[:, 0, :]
                clean_cls_state = clean_state[wrong_prediction, 0, :]

                difference = attacked_cls_state - clean_cls_state

                # print(difference.shape)

                difference_norm = torch.linalg.vector_norm(
                    x=difference,
                    ord=torch.inf,
                    dim=-1,
                ).cpu().numpy()

                all_difference_norms.append(difference_norm)
            
            diffs.append(all_difference_norms)  

diffs_numpy = np.squeeze(np.array(diffs))
mean_diffs = diffs_numpy.mean(axis=0)
stds_diffs = np.std(diffs_numpy, axis=0)
print(diffs_numpy.shape, mean_diffs.shape, stds_diffs.shape)

fig, ax = plt.subplots()
ax.boxplot(diffs_numpy)

# Overlay mean as red dots
plt.plot(range(len(mean_diffs)), mean_diffs, 'ro', label='Mean')

# Add error bars to show standard deviation
plt.errorbar(range(len(stds_diffs)), mean_diffs, yerr=stds_diffs, fmt='none', ecolor='gray', capsize=5, label='Std Dev')


ax.set_xlabel("Layer")
ax.set_xticklabels(
    ["Emb."] + [
        str(i) for i in range(1, len(mean_diffs))
    ],
)
ax.set_ylabel(f"Mean Linf norm ({attack_type} attack - clean outputs)")
plt.title(
    (
        "Geometric Distances of Hidden Representations for\n"
        f"over {END_DATAPOINT} datapoints\n"
        f"with {attack_type}"
    )
)
plt.show()
plt.savefig(f"standard_all_rep/{attack_type}_gemotry_dis.png")