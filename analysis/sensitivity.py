import random
import torch
import matplotlib.pyplot as plt
from transformers import (
    AutoModelForSequenceClassification,
    AutoTokenizer,
)
from datasets import load_dataset
import textattack
from textattack.attack_results import SuccessfulAttackResult

RANDOM_SEED = 0
MAX_NUM_WORD_SWAPS = 3
EPSILON = 1e-1
START_DATAPOINT = 0
END_DATAPOINT = 10
ITER_STEP = 1
SAMPLES = 100
MODEL_NAME = "fabriceyhc/bert-base-uncased-imdb"
# MODEL_NAME = "textattack/bert-base-uncased-imdb"

random.seed(RANDOM_SEED)
torch.manual_seed(RANDOM_SEED)

def project_l2(
    x: torch.Tensor,
    epsilon: float,
    dim: int = -1,
) -> torch.Tensor:
    """
    Projects the input tensor x onto the L2 ball of radius epsilon.

    Args:
        x: The input tensor to be projected.
        epsilon: The radius of the L2 ball.
    """

    norm = torch.linalg.vector_norm(
        x=x,
        ord=2,
        dim=dim,
        keepdim=True,
    )

    projected = (x / norm) * torch.clamp(norm, max=epsilon)

    return projected

model = AutoModelForSequenceClassification.from_pretrained(MODEL_NAME)
model = model.to("cuda")
model.eval()

embedding_layer = model.get_input_embeddings()
avg_embedding_norm = torch.linalg.vector_norm(
    x=embedding_layer.weight,
    ord=2,
    dim=1,
).mean()
epsilon = EPSILON * avg_embedding_norm

print("Average embedding norm:", avg_embedding_norm.item())
print("Epsilon:", epsilon.item())

tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)

model_wrapper = textattack.models.wrappers.HuggingFaceModelWrapper(
    model=model,
    tokenizer=tokenizer,
)

dataset = load_dataset("stanfordnlp/imdb")

attack = textattack.attack_recipes.Pruthi2019.build(
    model_wrapper=model_wrapper,
    max_num_word_swaps=MAX_NUM_WORD_SWAPS,
)

for i in range(START_DATAPOINT, END_DATAPOINT, ITER_STEP):
    datapoint = dataset["test"][i]
    prompt = datapoint["text"]
    label = datapoint["label"]

    print("Prompt:", prompt)
    print("Label:", label)

    inputs = tokenizer(
        prompt,
        padding=True,
        return_tensors="pt",
    ).to("cuda")

    if len(inputs["input_ids"][0]) > tokenizer.model_max_length:
        print("Input exceeds max length. Skipping...\n")

        continue

    with torch.no_grad():
        output = model(**inputs)
        original_prediction = torch.argmax(output.logits, dim=1)

    print("Original prediction:", original_prediction.item())

    if original_prediction != label:
        print("Original prediction does not match label. Skipping...\n")

        continue

    attack_result = attack.attack(
        example=prompt,
        ground_truth_output=label,
    )

    if isinstance(attack_result, SuccessfulAttackResult):
        attacked_prompt = attack_result.perturbed_text()

        print("Attacked prompt:", attacked_prompt)

        attacked_inputs = tokenizer(
            attacked_prompt,
            padding=True,
            return_tensors="pt",
        ).to("cuda")

        attacked_output = model(
            **attacked_inputs,
            output_hidden_states=True,
        )
        attacked_prediction = torch.argmax(attacked_output.logits, dim=1)
        attacked_hidden_states = attacked_output.hidden_states

        print("Attacked prediction:", attacked_prediction.item())

        embeddings = embedding_layer(
            attacked_inputs["input_ids"],
        ).repeat(SAMPLES, 1, 1)
        perturbation = torch.rand_like(embeddings) * epsilon
        perturbation = project_l2(
            x=perturbation,
            epsilon=epsilon,
            dim=-1,
        )
        perturbation[:, 0, :] = 0
        perturbed_embeddings = embeddings + perturbation

        with torch.no_grad():
            perturbed_output = model(
                inputs_embeds=perturbed_embeddings,
                attention_mask=inputs["attention_mask"],
                output_hidden_states=True,
            )
            perturbed_prediction = torch.argmax(perturbed_output.logits, dim=1)
            wrong_prediction = torch.nonzero(
                perturbed_prediction != label
            ).flatten()
            num_wrong = len(wrong_prediction)
            
            perturbed_hidden_states = perturbed_output.hidden_states

            all_difference_norms = []

            for (attacked_state, perturbed_state) in zip(
                attacked_hidden_states,
                perturbed_hidden_states,
            ):
                attacked_cls_state = attacked_state[:, 0, :]
                perturbed_cls_state = perturbed_state[wrong_prediction, 0, :]

                difference = attacked_cls_state - perturbed_cls_state

                difference_norm = torch.linalg.vector_norm(
                    x=difference,
                    ord=2,
                    dim=-1,
                ).cpu().numpy()

                all_difference_norms.append(difference_norm)
            
        
        fig, ax = plt.subplots()
        ax.boxplot(all_difference_norms)
        ax.set_xlabel("Layer")
        ax.set_xticklabels(
            ["Emb."] + [
                str(i) for i in range(1, len(all_difference_norms))
            ],
        )
        ax.set_ylabel("L2 norm (pert. attack - orig. attack)")
        plt.title(
            (
                "Sensitivity of [CLS] Token Hidden Representations for\n"
                f"Attacked Test Point {i} Over {num_wrong} Perturbations\n"
                "(Pruthi-3)"
            )
        )
        plt.show()
        plt.savefig(f"standard_all_rep/sensitivity_{i}.png")

        perturbed_accuracy = (perturbed_prediction == label).sum() \
            / SAMPLES * 100

        print(f"Perturbed attack accuracy: {perturbed_accuracy.item():.2f}%")
    
    print()